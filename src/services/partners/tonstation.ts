const TONSTATION_BASE_URL_DEV = 'https://dev.tonstation.app/openapi/api/v1'
const TONSTATION_BASE_URL_PROD = 'https://tonstation.app/openapi/api/v1'

// Use environment variables for API keys, with fallback to hardcoded values
const TONSTATION_API_KEY_DEV = import.meta.env.VITE_TONSTATION_API_KEY_DEV || '4397cc1c-fbd0-4a7f-8b04-a225e758924d'
const TONSTATION_API_KEY_PROD = import.meta.env.VITE_TONSTATION_API_KEY_PROD || '2e88e828-265e-4b73-9d75-7738b243a0bc'

// Automatically switch between dev/prod based on app environment
const isProduction = import.meta.env.VITE_APP_ENV === 'production'
const TONSTATION_BASE_URL = isProduction ? TONSTATION_BASE_URL_PROD : TONSTATION_BASE_URL_DEV
const TONSTATION_API_KEY = isProduction ? TONSTATION_API_KEY_PROD : TONSTATION_API_KEY_DEV

interface TonstationApiResponse {
  code: number
  message: string
  data: {
    completed?: boolean
    count?: number
  }
}

function getTonstationDailyComboUrl(userTelegramId: string) {
  return `${TONSTATION_BASE_URL}/dailyCombo/${userTelegramId}/completion`
}

function getTonstationLuckyWheelUrl(userTelegramId: string, dateStart: string) {
  return `${TONSTATION_BASE_URL}/luckyWheel/${userTelegramId}/spinsCount?dateStart=${encodeURIComponent(dateStart)}`
}

async function fetchTonstationApi(url: string): Promise<TonstationApiResponse | null> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'X-API-Key': TONSTATION_API_KEY
      }
    })

    if (response.ok) {
      const data: TonstationApiResponse = await response.json()

      // Check if API returned success
      if (data.code === 200 && data.message === 'success') {
        return data
      }
    }

    return null
  } catch (error) {
    // Only log edge cases - minimal logging as preferred
    if (error instanceof TypeError) {
      console.error('TON Station API network error:', error.message)
    }
    return null
  }
}

export async function checkTonstationDailyCombo(userTelegramId: string): Promise<boolean> {
  const url = getTonstationDailyComboUrl(userTelegramId)
  const response = await fetchTonstationApi(url)
  
  return response?.data?.completed || false
}

export async function checkTonstationLuckyWheel(userTelegramId: string): Promise<boolean> {
  // Get date from 30 days ago to check for spins
  const dateStart = new Date()
  dateStart.setDate(dateStart.getDate() - 30)
  const dateStartISO = dateStart.toISOString()
  
  const url = getTonstationLuckyWheelUrl(userTelegramId, dateStartISO)
  const response = await fetchTonstationApi(url)
  
  // Check if user has spun the wheel at least 5 times
  const spinCount = response?.data?.count || 0
  return spinCount >= 5
}
