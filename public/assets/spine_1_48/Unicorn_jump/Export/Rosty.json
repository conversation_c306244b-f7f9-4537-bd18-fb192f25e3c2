{"skeleton": {"hash": "s9wKpFrr8Zc", "spine": "4.2.40", "x": -82.59, "y": -97.71, "width": 161.3, "height": 208.26, "images": "./Images/Monster_Rosty/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "cntr", "parent": "root"}, {"name": "low_cntr", "parent": "cntr", "x": -7.83, "y": -76.1, "icon": "arrows"}, {"name": "body", "parent": "low_cntr", "length": 63.76, "rotation": 73.08, "x": 0.15, "y": 18.65}, {"name": "body2", "parent": "body", "length": 83.04, "rotation": 30.13, "x": 63.76}, {"name": "Arm_L", "parent": "body", "length": 21.67, "rotation": -142.33, "x": 49.08, "y": -19.18}, {"name": "Arm_L2", "parent": "Arm_L", "length": 51.54, "rotation": -36.11, "x": 21.67}, {"name": "eye", "parent": "body2", "length": 20, "rotation": -96.72, "x": 19.26, "y": 26.54, "icon": "eye"}, {"name": "pupil", "parent": "eye", "rotation": -6.5, "x": -2.02, "y": 5.04, "icon": "eye"}, {"name": "Eyelid", "parent": "body2", "x": 30.18, "y": 25.62, "icon": "romanI"}, {"name": "eyebrow", "parent": "Eyelid", "x": 9.14, "y": 7.09, "icon": "leaf"}, {"name": "mouth_base", "parent": "body", "x": 37.84, "y": 26, "icon": "mouth"}, {"name": "mouth_base2", "parent": "mouth_base", "length": 22.1, "rotation": 111.35, "x": -0.31, "y": 6.04}, {"name": "mouth_base3", "parent": "mouth_base", "length": 20.76, "rotation": -108.35, "x": 0.73, "y": -5.2}, {"name": "Controls", "parent": "root", "rotation": 90, "x": 104.69, "y": 64.7}, {"name": "Mouth_control", "parent": "Controls", "y": -12.52, "color": "abe323ff", "icon": "mouth"}, {"name": "Arm_R", "parent": "body", "length": 22.34, "rotation": 172.38, "x": 34.34, "y": 28.5}, {"name": "Arm_R2", "parent": "Arm_R", "length": 48.52, "rotation": 22.33, "x": 22.46, "y": -0.27}, {"name": "blot", "parent": "root", "rotation": -0.04, "icon": "flower"}, {"name": "Face", "parent": "Controls", "y": -29.95, "color": "abe323ff", "icon": "arrows"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "root"}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "ARM_R_outline", "bone": "Arm_R", "attachment": "ARM_R_outline"}, {"name": "body_outline", "bone": "body", "attachment": "body_outline"}, {"name": "ARM_L_outline", "bone": "Arm_L", "attachment": "ARM_L_outline"}, {"name": "mouth_outline", "bone": "mouth_base", "attachment": "mouth_outline"}, {"name": "Arm_R", "bone": "Arm_R", "attachment": "Arm_R"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "eye", "bone": "eye", "attachment": "eye"}, {"name": "pupil", "bone": "pupil", "attachment": "pupil"}, {"name": "eyebrow_base", "bone": "body", "attachment": "eyebrow_base"}, {"name": "mouth_base", "bone": "mouth_base", "attachment": "mouth_base"}, {"name": "mouth", "bone": "mouth_base", "attachment": "mouth"}, {"name": "Arm_L", "bone": "Arm_L", "attachment": "Arm_L"}, {"name": "eyebrow", "bone": "eyebrow", "attachment": "eyebrow"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop4", "bone": "blot_drop4"}], "transform": [{"name": "Eye_Face", "order": 1, "bones": ["eye"], "target": "Face", "rotation": -96.72, "local": true, "x": 19.26, "y": 56.49, "mixRotate": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "Mouth_Face", "bones": ["mouth_base"], "target": "Face", "local": true, "relative": true, "y": 29.55, "mixRotate": 0, "mixX": 0.6, "mixScaleX": 0, "mixShearY": 0}, {"name": "Mouth_L", "order": 2, "bones": ["mouth_base3"], "target": "Mouth_control", "rotation": -125.27, "x": -79.2, "y": 133.56, "mixRotate": 0.5, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "Mouth_R", "order": 3, "bones": ["mouth_base2"], "target": "Mouth_control", "rotation": 94.43, "x": -76.93, "y": 144.62, "mixRotate": -0.5, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"Arm_L": {"Arm_L": {"type": "mesh", "uvs": [0.86019, 0.00936, 1, 0.08686, 1, 0.18857, 0.96648, 0.28766, 0.91835, 0.39558, 0.85326, 0.49633, 0.78818, 0.59708, 0.72225, 0.6845, 0.64173, 0.77309, 0.55438, 0.85159, 0.46434, 0.92418, 0.36639, 0.9784, 0.23404, 0.977, 0.17132, 0.93284, 0.12849, 0.83058, 0.09587, 0.72615, 0.07151, 0.62426, 0.01217, 0.53692, 0.0121, 0.47755, 0.03515, 0.41164, 0.08356, 0.33268, 0.1517, 0.25269, 0.22449, 0.19075, 0.33929, 0.11347, 0.4691, 0.05988, 0.63771, 0.00936, 0.28973, 0.37175, 0.4542, 0.59618, 0.64656, 0.24537], "triangles": [26, 17, 18, 27, 28, 5, 6, 27, 5, 18, 19, 26, 16, 26, 27, 16, 17, 26, 7, 27, 6, 15, 16, 27, 8, 27, 7, 14, 15, 27, 9, 14, 27, 8, 9, 27, 10, 14, 9, 13, 14, 10, 11, 12, 13, 10, 11, 13, 0, 1, 2, 28, 25, 0, 28, 0, 2, 24, 25, 28, 3, 28, 2, 26, 22, 23, 21, 22, 26, 20, 21, 26, 4, 28, 3, 5, 28, 4, 23, 28, 26, 28, 23, 24, 27, 26, 28, 26, 19, 20], "vertices": [2, 5, -2.78, 34.16, 0.9965, 6, -39.89, 13.18, 0.0035, 2, 5, 7.37, 40.62, 0.97289, 6, -35.49, 24.39, 0.02711, 2, 5, 16.22, 37.27, 0.9217, 6, -26.37, 26.9, 0.0783, 2, 5, 24.02, 31.84, 0.79182, 6, -16.87, 27.11, 0.20818, 2, 5, 32.23, 25.18, 0.52641, 6, -6.31, 26.56, 0.47359, 2, 5, 39.4, 17.66, 0.20886, 6, 3.91, 24.72, 0.79114, 2, 5, 46.57, 10.14, 0.02647, 6, 14.14, 22.87, 0.97353, 1, 6, 23.18, 20.63, 1, 1, 6, 32.6, 17.46, 1, 1, 6, 41.23, 13.58, 1, 1, 6, 49.39, 9.38, 1, 1, 6, 56.04, 4.2, 1, 1, 6, 58.33, -4.64, 1, 1, 6, 55.52, -9.9, 1, 2, 5, 50.74, -40.12, 0.00148, 6, 47.13, -15.27, 0.99852, 2, 5, 40.86, -38.78, 0.02193, 6, 38.36, -20.02, 0.97807, 2, 5, 31.41, -36.99, 0.09811, 6, 29.67, -24.15, 0.90189, 2, 5, 22.36, -37.94, 0.21136, 6, 22.92, -30.25, 0.78864, 2, 5, 17.2, -35.99, 0.2803, 6, 17.6, -31.71, 0.7197, 2, 5, 12.03, -32.33, 0.38838, 6, 11.27, -31.8, 0.61162, 2, 5, 6.34, -26.61, 0.56689, 6, 3.3, -30.53, 0.43311, 2, 5, 1.05, -19.58, 0.77659, 6, -5.12, -27.96, 0.22341, 2, 5, -2.55, -12.84, 0.91766, 6, -12, -24.65, 0.08234, 2, 5, -6.47, -2.88, 0.99784, 6, -21.03, -18.91, 0.00216, 1, 5, -7.95, 7.26, 1, 1, 5, -8.22, 19.8, 1, 2, 5, 14.78, -14.59, 0.58248, 6, 3.04, -15.85, 0.41752, 1, 6, 20.16, 0.62, 1, 2, 5, 12.52, 12.6, 0.95811, 6, -14.82, 4.78, 0.04189], "hull": 26, "edges": [0, 50, 0, 2, 20, 22, 22, 24, 6, 8, 18, 20, 16, 18, 12, 14, 14, 16, 8, 10, 10, 12, 2, 4, 4, 6, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 46, 48, 48, 50, 44, 46, 40, 42, 42, 44, 36, 38, 38, 40], "width": 69, "height": 93}}, "ARM_L_outline": {"ARM_L_outline": {"type": "mesh", "uvs": [0.8451, 0.0172, 0.94536, 0.071, 0.98885, 0.15405, 0.99628, 0.24564, 0.96656, 0.32935, 0.92593, 0.43243, 0.84629, 0.56202, 0.76855, 0.67523, 0.68152, 0.77911, 0.58642, 0.86864, 0.48448, 0.94663, 0.3813, 0.98291, 0.24795, 0.98205, 0.14589, 0.90332, 0.09818, 0.78193, 0.08292, 0.68607, 0.0463, 0.60095, 1e-05, 0.53249, 0.0065, 0.45094, 0.05363, 0.35664, 0.14627, 0.24442, 0.28233, 0.13321, 0.45153, 0.05514, 0.65789, 0.00215], "triangles": [10, 11, 13, 11, 12, 13, 9, 10, 14, 10, 13, 14, 8, 9, 15, 9, 14, 15, 8, 15, 7, 15, 16, 7, 16, 19, 7, 7, 19, 6, 6, 19, 20, 17, 18, 16, 16, 18, 19, 6, 20, 21, 6, 21, 22, 5, 6, 23, 23, 6, 22, 4, 5, 23, 3, 23, 2, 23, 3, 4, 2, 23, 0, 2, 0, 1], "vertices": [2, 5, -6.42, 38.85, 0.9946, 6, -45.59, 14.83, 0.0054, 2, 5, 1.74, 44.44, 0.97562, 6, -42.29, 24.16, 0.02438, 2, 5, 11.14, 44.65, 0.93557, 6, -34.82, 29.86, 0.06443, 2, 5, 20.35, 41.8, 0.85597, 6, -25.7, 32.99, 0.14403, 2, 5, 27.71, 36.44, 0.72132, 6, -16.59, 33, 0.27868, 2, 5, 36.67, 29.52, 0.46802, 6, -5.28, 32.69, 0.53198, 2, 5, 47.11, 18.67, 0.12838, 6, 9.55, 30.07, 0.87162, 2, 5, 55.99, 8.57, 0.00767, 6, 22.68, 27.15, 0.99233, 1, 6, 35.06, 23.24, 1, 1, 6, 46.17, 18.3, 1, 1, 6, 56.25, 12.51, 1, 1, 6, 62.14, 5.46, 1, 1, 6, 64.91, -4.98, 1, 1, 6, 59.13, -15.14, 1, 2, 5, 47.23, -46.18, 0.00315, 6, 47.86, -22.24, 0.99685, 2, 5, 37.37, -43.77, 0.02881, 6, 38.48, -26.1, 0.97119, 2, 5, 27.97, -43.37, 0.0955, 6, 30.65, -31.33, 0.9045, 2, 5, 19.92, -44.33, 0.16703, 6, 24.71, -36.85, 0.83297, 2, 5, 12.1, -40.81, 0.25578, 6, 16.32, -38.61, 0.74422, 2, 5, 4.19, -33.73, 0.42427, 6, 5.76, -37.55, 0.57573, 2, 5, -4.17, -22.54, 0.72037, 6, -7.59, -33.43, 0.27963, 2, 5, -11.19, -8.09, 0.96653, 6, -21.77, -25.9, 0.03347, 1, 5, -13.99, 7.63, 1, 1, 5, -13.27, 25.23, 1], "hull": 24, "edges": [0, 46, 34, 36, 40, 42, 42, 44, 44, 46, 8, 10, 10, 12, 18, 20, 20, 22, 16, 18, 12, 14, 14, 16, 4, 6, 6, 8, 0, 2, 2, 4, 22, 24, 26, 28, 24, 26, 32, 34, 28, 30, 30, 32, 36, 38, 38, 40], "width": 81, "height": 105}}, "Arm_R": {"Arm_R": {"type": "mesh", "uvs": [0.62169, 0.03127, 0.81982, 0.09723, 0.93733, 0.20969, 0.97663, 0.31227, 0.97672, 0.45691, 0.97682, 0.59655, 0.88773, 0.7236, 0.76413, 0.84253, 0.61678, 0.93922, 0.51083, 0.98491, 0.29106, 0.9846, 0.17112, 0.88751, 0.12514, 0.76695, 0.08614, 0.66468, 0.05367, 0.56028, 0.04714, 0.45532, 0.04713, 0.35814, 0.04713, 0.26096, 0.09765, 0.13842, 0.22675, 0.05423, 0.42932, 0.01024], "triangles": [0, 2, 3, 2, 0, 1, 19, 20, 0, 8, 9, 11, 9, 10, 11, 7, 8, 12, 8, 11, 12, 6, 7, 13, 7, 12, 13, 5, 6, 13, 4, 17, 18, 5, 13, 4, 4, 18, 3, 0, 3, 18, 18, 19, 0, 14, 15, 4, 4, 13, 14, 4, 15, 16, 4, 16, 17], "vertices": [2, 17, -8.36, 5.87, 0.21366, 16, 12.5, 1.98, 0.78634, 2, 17, -4.47, 14.35, 0.12775, 16, 12.88, 11.3, 0.87225, 2, 17, 2.54, 19.55, 0.22381, 16, 17.38, 18.78, 0.77619, 2, 17, 9.03, 21.46, 0.42083, 16, 22.67, 23.01, 0.57917, 2, 17, 18.28, 21.82, 0.63822, 16, 31.09, 26.86, 0.36178, 2, 17, 27.21, 22.16, 0.81228, 16, 39.21, 30.57, 0.18772, 2, 17, 35.48, 18.74, 0.92272, 16, 48.16, 30.55, 0.07728, 2, 17, 43.29, 13.84, 0.97453, 16, 57.24, 28.99, 0.02547, 2, 17, 49.71, 7.9, 0.99398, 16, 65.44, 25.93, 0.00602, 2, 17, 52.8, 3.56, 0.99933, 16, 69.95, 23.09, 0.00067, 2, 17, 53.14, -5.66, 1, 16, 73.77, 14.69, 0, 1, 17, 47.12, -10.93, 1, 1, 17, 39.49, -13.16, 1, 1, 17, 33.01, -15.05, 1, 1, 17, 26.39, -16.67, 1, 1, 17, 19.68, -17.2, 1, 1, 17, 13.47, -17.44, 1, 2, 17, 7.25, -17.68, 0.99559, 16, 35.89, -13.87, 0.00441, 2, 17, -0.66, -15.86, 0.9226, 16, 27.88, -15.19, 0.0774, 2, 17, -6.26, -10.65, 0.73864, 16, 20.72, -12.5, 0.26136, 2, 17, -9.4, -2.26, 0.45348, 16, 14.63, -5.93, 0.54652], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 10, 12, 16, 18, 18, 20, 34, 36, 36, 38, 38, 40, 6, 8, 8, 10, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 20, 22], "width": 42, "height": 64}}, "ARM_R_outline": {"ARM_R_outline": {"type": "mesh", "uvs": [0.63593, 0.02801, 0.80768, 0.08961, 0.93536, 0.20991, 0.98167, 0.33975, 0.98179, 0.4529, 0.9803, 0.56261, 0.94409, 0.66561, 0.88372, 0.75145, 0.77614, 0.85074, 0.66705, 0.93097, 0.52012, 0.98705, 0.31299, 0.98698, 0.27661, 0.97351, 0.20354, 0.92171, 0.14392, 0.85731, 0.10487, 0.74627, 0.06582, 0.63524, 0.0361, 0.52604, 0.03648, 0.40448, 0.03685, 0.28292, 0.074, 0.19035, 0.16346, 0.09541, 0.28425, 0.03392, 0.47296, 0.01185], "triangles": [2, 0, 1, 0, 3, 23, 2, 3, 0, 14, 9, 10, 13, 10, 11, 11, 12, 13, 15, 8, 9, 14, 10, 13, 9, 14, 15, 8, 15, 7, 6, 7, 4, 16, 4, 7, 21, 4, 16, 23, 3, 21, 23, 21, 22, 16, 7, 15, 19, 20, 21, 5, 6, 4, 17, 18, 16, 21, 16, 18, 19, 21, 18, 21, 3, 4], "vertices": [1, 16, 6.23, 1.57, 1, 2, 17, -10, 17.32, 0.00719, 16, 6.64, 11.95, 0.99281, 2, 17, -1.13, 24.56, 0.15425, 16, 12.09, 22.02, 0.84575, 2, 17, 8.64, 27.44, 0.40533, 16, 20.03, 28.39, 0.59467, 2, 17, 17.23, 27.77, 0.63317, 16, 27.85, 31.97, 0.36683, 2, 17, 25.57, 28.02, 0.79833, 16, 35.47, 35.36, 0.20167, 2, 17, 33.46, 26.36, 0.89635, 16, 43.4, 36.83, 0.10365, 2, 17, 40.11, 23.36, 0.95087, 16, 50.69, 36.58, 0.04913, 2, 17, 47.87, 17.84, 0.98879, 16, 59.97, 34.43, 0.01121, 2, 17, 54.19, 12.19, 0.99909, 16, 67.96, 31.6, 0.00091, 1, 17, 58.76, 4.42, 1, 1, 17, 59.18, -6.75, 1, 1, 17, 58.23, -8.75, 1, 1, 17, 54.45, -12.85, 1, 1, 17, 49.68, -16.25, 1, 1, 17, 41.33, -18.69, 1, 1, 17, 32.98, -21.12, 1, 1, 17, 24.75, -23.04, 1, 1, 17, 15.52, -23.38, 1, 1, 17, 6.29, -23.71, 1, 1, 17, -0.82, -21.98, 1, 2, 17, -8.22, -17.43, 0.98613, 16, 21.49, -19.51, 0.01387, 2, 17, -13.14, -11.09, 0.83234, 16, 14.52, -15.52, 0.16766, 2, 17, -15.21, -0.97, 0.25798, 16, 8.77, -6.94, 0.74202], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 6, 8, 8, 10, 34, 36, 36, 38, 28, 30, 30, 32, 10, 12, 12, 14], "width": 54, "height": 76}}, "blot": {"blot": {"type": "mesh", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [11, 12, 123, 123, 14, 15, 111, 112, 0, 122, 96, 101, 77, 78, 122, 79, 122, 78, 122, 79, 80, 96, 97, 99, 97, 98, 99, 87, 88, 119, 116, 0, 113, 115, 116, 114, 3, 0, 2, 2, 0, 1, 117, 50, 51, 117, 52, 53, 117, 51, 52, 120, 34, 35, 120, 35, 36, 37, 120, 36, 38, 120, 37, 38, 39, 120, 120, 39, 40, 117, 58, 59, 69, 118, 68, 71, 72, 118, 70, 71, 118, 86, 119, 85, 86, 87, 119, 69, 70, 118, 57, 58, 117, 56, 57, 117, 59, 60, 117, 56, 117, 55, 40, 41, 120, 55, 117, 54, 117, 53, 54, 123, 13, 14, 116, 113, 114, 85, 119, 84, 119, 88, 89, 119, 89, 90, 3, 4, 0, 111, 0, 4, 0, 112, 113, 123, 12, 13, 41, 42, 120, 43, 44, 31, 43, 31, 42, 42, 32, 120, 83, 94, 95, 120, 33, 34, 120, 32, 33, 45, 122, 44, 101, 96, 99, 31, 122, 30, 29, 30, 122, 122, 20, 21, 122, 121, 29, 21, 22, 121, 121, 122, 21, 29, 121, 28, 101, 108, 122, 108, 103, 107, 106, 107, 105, 101, 103, 108, 105, 107, 103, 28, 121, 27, 101, 102, 103, 105, 103, 104, 20, 9, 123, 20, 122, 9, 9, 10, 123, 10, 11, 123, 108, 109, 122, 109, 6, 122, 6, 7, 122, 122, 8, 9, 122, 7, 8, 27, 121, 26, 26, 121, 25, 121, 24, 25, 121, 23, 24, 121, 22, 23, 20, 123, 19, 19, 123, 18, 109, 110, 6, 18, 123, 17, 110, 5, 6, 110, 4, 5, 4, 110, 111, 123, 16, 17, 123, 15, 16, 31, 44, 122, 95, 96, 122, 101, 99, 100, 65, 74, 122, 122, 74, 75, 64, 65, 122, 76, 122, 75, 47, 64, 122, 77, 122, 76, 122, 80, 81, 81, 82, 122, 47, 122, 46, 82, 83, 122, 46, 122, 45, 122, 83, 95, 42, 31, 32, 68, 118, 67, 72, 73, 118, 60, 61, 117, 67, 118, 66, 73, 74, 118, 66, 118, 65, 61, 62, 117, 65, 118, 74, 62, 48, 117, 117, 49, 50, 117, 48, 49, 62, 63, 48, 48, 63, 47, 63, 64, 47, 93, 119, 92, 119, 93, 84, 90, 91, 119, 119, 91, 92, 84, 93, 83, 93, 94, 83], "vertices": [2, 18, -90.46, 160, 0.02155, 20, -90.46, 170.67, 0.97845, 2, 18, -78.96, 159.99, 0.00946, 20, -78.96, 170.67, 0.99054, 2, 18, -69.06, 154.46, 0.153, 20, -69.06, 165.13, 0.847, 2, 18, -62.08, 145.27, 0.39795, 20, -62.08, 155.94, 0.60205, 2, 18, -57.73, 133.84, 0.70584, 20, -57.73, 144.52, 0.29416, 2, 18, -54.31, 122.41, 0.93146, 20, -54.31, 133.09, 0.06854, 2, 18, -51.33, 117.11, 0.95362, 20, -51.33, 127.78, 0.04638, 2, 18, -14.95, 126.15, 0.7559, 20, -14.95, 136.83, 0.2441, 2, 18, 24.35, 125.92, 0.76179, 20, 24.35, 136.6, 0.23821, 2, 18, 61.21, 115.48, 0.74063, 20, 61.21, 126.16, 0.25937, 2, 18, 64.89, 123.14, 0.64513, 20, 64.89, 133.82, 0.35487, 2, 18, 70.31, 134.89, 0.36729, 20, 70.31, 145.56, 0.63271, 2, 18, 80.47, 139.96, 0.19886, 20, 80.47, 150.63, 0.80114, 2, 18, 95.66, 140.83, 0.10171, 20, 95.66, 151.5, 0.89829, 2, 18, 108.56, 135.35, 0.19647, 20, 108.56, 146.02, 0.80353, 2, 18, 117.62, 125.35, 0.36288, 20, 117.62, 136.03, 0.63712, 2, 18, 121.49, 113.79, 0.46208, 20, 121.49, 124.46, 0.53792, 2, 18, 121.69, 98.88, 0.59174, 20, 121.69, 109.55, 0.40826, 2, 18, 114.64, 89.74, 0.74078, 20, 114.64, 100.42, 0.25922, 2, 18, 106.9, 83.73, 0.86799, 20, 106.9, 94.4, 0.13201, 1, 18, 103.35, 77.75, 1, 1, 18, 109.67, 66.42, 1, 2, 18, 119.96, 67.11, 0.98349, 20, 119.96, 77.79, 0.01651, 2, 18, 132.78, 61.13, 0.81787, 20, 132.78, 71.8, 0.18213, 2, 18, 141.05, 52.69, 0.70315, 20, 141.05, 63.37, 0.29685, 2, 18, 146.38, 40.36, 0.62876, 20, 146.38, 51.04, 0.37124, 2, 18, 146.29, 28.49, 0.60882, 20, 146.29, 39.17, 0.39118, 2, 18, 144.11, 14.31, 0.63067, 20, 144.11, 24.99, 0.36933, 2, 18, 136.31, 6.06, 0.74414, 20, 136.31, 16.73, 0.25586, 2, 18, 126.93, -0.54, 0.88743, 20, 126.93, 10.14, 0.11257, 1, 18, 125.67, -20.51, 1, 2, 18, 121.63, -37.78, 0.92415, 20, 121.63, -27.1, 0.07585, 2, 18, 129.26, -39.73, 0.85626, 20, 129.26, -29.06, 0.14374, 2, 18, 140.76, -41.86, 0.65141, 20, 140.76, -31.18, 0.34859, 2, 18, 147.81, -44.93, 0.45337, 20, 147.81, -34.26, 0.54663, 2, 18, 153.4, -51.19, 0.22414, 20, 153.4, -40.51, 0.77586, 2, 18, 154.61, -57.83, 0.12863, 20, 154.61, -47.15, 0.87137, 2, 18, 152.64, -65.97, 0.06435, 20, 152.64, -55.29, 0.93565, 2, 18, 146.18, -71.79, 0.0759, 20, 146.18, -61.12, 0.9241, 2, 18, 138.88, -73.6, 0.11319, 20, 138.88, -62.93, 0.88681, 2, 18, 131.77, -72.77, 0.22246, 20, 131.77, -62.1, 0.77754, 2, 18, 126.5, -67.65, 0.41853, 20, 126.5, -56.97, 0.58147, 2, 18, 121.97, -59.8, 0.66246, 20, 121.97, -49.12, 0.33754, 2, 18, 114.11, -56.19, 0.99426, 20, 114.11, -45.51, 0.00574, 2, 18, 109.4, -62.16, 0.99314, 20, 109.4, -51.49, 0.00686, 2, 18, 104.08, -70.25, 0.99213, 20, 104.08, -59.58, 0.00787, 2, 18, 98.24, -77.29, 0.99754, 20, 98.24, -66.61, 0.00246, 2, 18, 94.36, -83.33, 0.60983, 20, 94.36, -72.66, 0.39017, 2, 18, 97.33, -89.75, 0.67939, 20, 97.33, -79.07, 0.32061, 2, 18, 103.43, -94.13, 0.58167, 20, 103.43, -83.45, 0.41833, 2, 18, 109.54, -96.01, 0.43988, 20, 109.54, -85.33, 0.56012, 2, 18, 115.81, -101.25, 0.3264, 20, 115.81, -90.58, 0.6736, 2, 18, 121.25, -105.85, 0.20757, 20, 121.25, -95.17, 0.79243, 2, 18, 124.38, -113.6, 0.2256, 20, 124.38, -102.93, 0.7744, 2, 18, 124.42, -121.7, 0.33257, 20, 124.42, -111.02, 0.66743, 2, 18, 123.55, -131.82, 0.27292, 20, 123.55, -121.15, 0.72708, 2, 18, 118.59, -138.56, 0.13068, 20, 118.59, -127.88, 0.86932, 1, 20, 109.99, -133.51, 1, 1, 20, 96.24, -134.55, 1, 2, 18, 85.73, -143.03, 0.09602, 20, 85.73, -132.35, 0.90398, 2, 18, 78.15, -134.98, 0.39368, 20, 78.15, -124.31, 0.60632, 2, 18, 73.51, -125.82, 0.70124, 20, 73.51, -115.15, 0.29876, 2, 18, 71.94, -117.26, 0.86966, 20, 71.94, -106.58, 0.13034, 2, 18, 68.6, -109.82, 0.95079, 20, 68.6, -99.14, 0.04921, 2, 18, 61.39, -108.03, 0.97473, 20, 61.39, -97.36, 0.02527, 2, 18, 55.07, -111.34, 0.95012, 20, 55.07, -100.66, 0.04988, 2, 18, 53.57, -123.32, 0.83978, 20, 53.57, -112.64, 0.16022, 2, 18, 51.29, -131.94, 0.7483, 20, 51.29, -121.27, 0.2517, 2, 18, 43.94, -142.19, 0.50798, 20, 43.94, -131.51, 0.49202, 2, 18, 33.03, -148.3, 0.20256, 20, 33.03, -137.62, 0.79744, 2, 18, 21.01, -149.16, 0.00552, 20, 21.01, -138.48, 0.99448, 1, 20, 8.04, -136.87, 1, 2, 18, -2.08, -139.51, 0.29991, 20, -2.08, -128.83, 0.70009, 2, 18, -8.02, -129.69, 0.61942, 20, -8.02, -119.01, 0.38058, 2, 18, -12.78, -121.81, 0.74609, 20, -12.78, -111.14, 0.25391, 2, 18, -31.03, -116.32, 0.81636, 20, -31.03, -105.65, 0.18364, 2, 18, -44.69, -111.25, 0.63451, 20, -44.69, -100.57, 0.36549, 2, 18, -56.87, -105.28, 0.59263, 20, -56.87, -94.6, 0.40737, 2, 18, -67.63, -105.33, 0.49938, 20, -67.63, -94.65, 0.50062, 2, 18, -76.03, -103.97, 0.44983, 20, -76.03, -93.3, 0.55017, 2, 18, -82.89, -98.08, 0.5575, 20, -82.89, -87.41, 0.4425, 2, 18, -87.96, -89.29, 0.81245, 20, -87.96, -78.61, 0.18755, 2, 18, -88.24, -80.38, 0.9861, 20, -88.24, -69.7, 0.0139, 2, 18, -95.71, -73.02, 0.99035, 20, -95.71, -62.34, 0.00965, 2, 18, -103.83, -79.34, 0.52724, 20, -103.83, -68.67, 0.47276, 1, 20, -112.87, -79.63, 1, 1, 20, -121.68, -88.38, 1, 1, 20, -136.77, -88.5, 1, 2, 18, -141.84, -94.13, 0.08531, 20, -141.84, -83.45, 0.91469, 2, 18, -144.69, -85.11, 0.33535, 20, -144.69, -74.43, 0.66465, 2, 18, -140.67, -76.76, 0.46148, 20, -140.67, -66.08, 0.53852, 2, 18, -135.95, -69.73, 0.55661, 20, -135.95, -59.05, 0.44339, 2, 18, -124.21, -65.72, 0.68332, 20, -124.21, -55.05, 0.31668, 2, 18, -111.76, -63.04, 0.77501, 20, -111.76, -52.36, 0.22499, 2, 18, -107.46, -57.63, 0.83963, 20, -107.46, -46.95, 0.16037, 1, 18, -108.21, -51.16, 1, 2, 18, -112.49, -42.83, 0.39981, 20, -112.49, -32.16, 0.60019, 2, 18, -121.27, -38.55, 0.33627, 20, -121.27, -27.88, 0.66373, 2, 18, -129.25, -29.94, 0.50107, 20, -129.25, -19.26, 0.49893, 2, 18, -132.01, -21.31, 0.61634, 20, -132.01, -10.63, 0.38366, 2, 18, -131.8, -7.46, 0.65964, 20, -131.8, 3.21, 0.34036, 2, 18, -127.78, 0.37, 0.65438, 20, -127.78, 11.05, 0.34562, 2, 18, -131.82, 6.83, 0.6746, 20, -131.82, 17.5, 0.3254, 2, 18, -135.8, 20.35, 0.90838, 20, -135.8, 31.02, 0.09162, 2, 18, -135.79, 39.61, 0.80863, 20, -135.79, 50.29, 0.19137, 2, 18, -131.21, 52.44, 0.71293, 20, -131.21, 63.11, 0.28707, 2, 18, -121.97, 65.54, 0.68699, 20, -121.97, 76.22, 0.31301, 2, 18, -110.44, 73.33, 0.78083, 20, -110.44, 84.01, 0.21917, 2, 18, -96.46, 77.56, 0.94418, 20, -96.46, 88.24, 0.05582, 2, 18, -89.34, 87.95, 0.67426, 20, -89.34, 98.63, 0.32574, 2, 18, -94.18, 94.23, 0.67915, 20, -94.18, 104.9, 0.32085, 2, 18, -103.39, 100.11, 0.53234, 20, -103.39, 110.78, 0.46766, 2, 18, -112.81, 107.01, 0.55156, 20, -112.81, 117.68, 0.44844, 2, 18, -120.79, 120.27, 0.28889, 20, -120.79, 130.95, 0.71111, 2, 18, -120.8, 138.84, 0.03582, 20, -120.8, 149.51, 0.96418, 1, 20, -114.81, 161.8, 1, 1, 20, -103.8, 169.35, 1, 2, 18, 101.18, -120.61, 0.71328, 20, 101.18, -109.94, 0.28672, 2, 18, 22.19, -127.14, 0.72168, 20, 22.19, -116.47, 0.27832, 2, 18, -124.9, -80.29, 0.48636, 20, -124.9, -69.61, 0.51364, 2, 18, 138.69, -56.86, 0.48188, 20, 138.69, -46.18, 0.51812, 2, 18, 122.35, 38.34, 0.90859, 20, 122.35, 49.02, 0.09141, 2, 18, 1.69, 12.22, 0.31422, 20, 1.69, 22.9, 0.68578, 2, 18, 94.94, 112.42, 0.81578, 20, 94.94, 123.1, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "body": {"body": {"type": "mesh", "uvs": [0.79359, 0.00326, 0.86024, 0.02615, 0.91158, 0.06236, 0.95025, 0.11136, 0.97529, 0.16939, 0.99171, 0.24282, 0.99346, 0.31168, 0.99346, 0.37694, 0.99346, 0.44219, 0.99038, 0.51033, 0.97555, 0.59132, 0.9531, 0.6707, 0.92338, 0.74335, 0.88847, 0.80309, 0.84627, 0.85871, 0.79249, 0.90356, 0.72305, 0.94346, 0.6505, 0.9725, 0.58156, 0.98921, 0.39342, 0.98924, 0.26862, 0.96487, 0.21318, 0.93613, 0.16537, 0.89671, 0.13898, 0.85375, 0.13889, 0.77189, 0.16541, 0.73607, 0.19035, 0.6957, 0.20257, 0.65291, 0.19326, 0.60535, 0.17424, 0.55971, 0.15632, 0.52023, 0.13466, 0.47794, 0.11518, 0.44103, 0.09258, 0.40386, 0.06967, 0.36644, 0.04801, 0.32799, 0.02635, 0.28954, 0.01063, 0.25225, 0.01235, 0.18711, 0.03294, 0.14677, 0.07982, 0.10818, 0.1245, 0.09017, 0.20937, 0.08199, 0.29555, 0.08011, 0.37773, 0.07561, 0.47117, 0.06385, 0.56377, 0.03307, 0.65158, 0.00525, 0.32436, 0.83314, 0.75225, 0.67346, 0.78828, 0.48864, 0.75405, 0.20921, 0.27031, 0.23804, 0.39012, 0.48864], "triangles": [51, 47, 0, 51, 0, 1, 51, 1, 2, 51, 2, 3, 51, 3, 4, 52, 42, 43, 51, 4, 5, 38, 40, 37, 40, 36, 37, 41, 42, 52, 38, 39, 40, 52, 35, 36, 40, 41, 52, 52, 36, 40, 34, 35, 52, 6, 51, 5, 33, 34, 52, 32, 33, 52, 51, 6, 7, 53, 31, 32, 45, 52, 44, 52, 43, 44, 52, 51, 53, 53, 32, 52, 7, 50, 51, 8, 50, 7, 9, 50, 8, 30, 31, 53, 46, 47, 51, 29, 30, 53, 10, 50, 9, 28, 29, 53, 45, 46, 51, 51, 52, 45, 50, 53, 51, 27, 28, 53, 10, 49, 50, 50, 49, 53, 11, 49, 10, 12, 49, 11, 13, 49, 12, 48, 27, 53, 48, 53, 49, 26, 27, 48, 25, 26, 48, 24, 25, 48, 23, 24, 48, 14, 49, 13, 48, 49, 17, 22, 23, 48, 14, 15, 49, 21, 22, 48, 15, 16, 49, 20, 21, 48, 16, 17, 49, 48, 19, 20, 17, 18, 48, 18, 19, 48], "vertices": [1, 4, 91.47, -53.85, 1, 1, 4, 85.07, -62.68, 1, 1, 4, 76.81, -68.71, 1, 2, 3, 157.75, -29.07, 0.00082, 4, 66.7, -72.33, 0.99918, 2, 3, 148.64, -35.79, 0.00609, 4, 55.44, -73.57, 0.99391, 2, 3, 136.43, -42.1, 0.02533, 4, 41.72, -72.89, 0.97467, 2, 3, 124.39, -46.04, 0.06602, 4, 29.32, -70.25, 0.93398, 2, 3, 112.9, -49.53, 0.13719, 4, 17.63, -67.51, 0.86281, 2, 3, 101.42, -53.03, 0.24565, 4, 5.94, -64.76, 0.75435, 2, 3, 89.28, -56.23, 0.39113, 4, -6.16, -61.45, 0.60887, 2, 3, 74.38, -58.43, 0.59126, 4, -20.15, -55.86, 0.40874, 2, 3, 59.42, -59.43, 0.77751, 4, -33.6, -49.22, 0.22249, 2, 3, 45.32, -59.03, 0.90198, 4, -45.58, -41.79, 0.09802, 2, 3, 33.27, -57.18, 0.9642, 4, -55.08, -34.15, 0.0358, 2, 3, 21.62, -54.07, 0.99181, 4, -63.58, -25.61, 0.00819, 2, 3, 11.37, -48.7, 0.99954, 4, -69.76, -15.81, 0.00046, 1, 3, 1.29, -40.8, 1, 1, 3, -7.01, -31.88, 1, 1, 3, -12.98, -22.81, 1, 1, 3, -21.25, 4.36, 1, 1, 3, -22.45, 23.7, 1, 2, 3, -19.83, 33.25, 0.99906, 4, -55.6, 70.72, 0.00094, 2, 3, -14.99, 42.26, 0.99454, 4, -46.89, 76.09, 0.00546, 2, 3, -8.58, 48.38, 0.98704, 4, -38.28, 78.16, 0.01296, 2, 3, 5.82, 52.77, 0.95764, 4, -23.61, 74.73, 0.04236, 2, 3, 13.29, 50.86, 0.928, 4, -18.11, 69.32, 0.072, 2, 3, 21.5, 49.42, 0.85975, 4, -11.74, 63.96, 0.14025, 2, 3, 29.57, 49.94, 0.74511, 4, -4.49, 60.36, 0.25489, 2, 3, 37.53, 53.84, 0.58684, 4, 4.34, 59.73, 0.41316, 2, 3, 44.73, 59.03, 0.43549, 4, 13.17, 60.61, 0.56451, 2, 3, 50.89, 63.73, 0.32295, 4, 20.86, 61.58, 0.67705, 2, 3, 57.38, 69.12, 0.22287, 4, 29.19, 62.98, 0.77713, 2, 3, 63.02, 73.91, 0.15505, 4, 36.47, 64.29, 0.84495, 2, 3, 68.57, 79.17, 0.10241, 4, 43.91, 66.05, 0.89759, 2, 3, 74.15, 84.48, 0.0651, 4, 51.4, 67.85, 0.9349, 2, 3, 79.97, 89.67, 0.03904, 4, 59.04, 69.41, 0.96096, 2, 3, 85.79, 94.86, 0.02231, 4, 66.67, 70.98, 0.97769, 2, 3, 91.66, 99.13, 0.01308, 4, 73.9, 71.72, 0.98692, 2, 3, 103.2, 102.37, 0.00441, 4, 85.5, 68.73, 0.99559, 2, 3, 111.21, 101.55, 0.00173, 4, 92.02, 64.01, 0.99827, 2, 3, 120.06, 96.85, 0.00022, 4, 97.31, 55.49, 0.99978, 1, 4, 99, 48.17, 1, 1, 4, 97.53, 35.35, 1, 1, 4, 94.89, 22.6, 1, 1, 4, 92.86, 10.33, 1, 1, 4, 91.75, -3.9, 1, 1, 4, 94.06, -18.81, 1, 1, 4, 96.01, -32.89, 1, 2, 3, 3.19, 22.7, 0.92542, 4, -40.99, 50.04, 0.07458, 2, 3, 50.1, -30.56, 0.70411, 4, -27.16, -19.58, 0.29589, 2, 3, 84.22, -25.87, 0.41864, 4, 4.71, -32.65, 0.58136, 2, 3, 131.91, -5.96, 0.10848, 4, 55.94, -39.37, 0.89152, 2, 3, 105.57, 62.37, 0.14414, 4, 67.48, 32.95, 0.85586, 2, 3, 66.72, 31.65, 0.38673, 4, 18.45, 25.88, 0.61327], "hull": 48, "edges": [0, 94, 0, 2, 2, 4, 8, 10, 22, 24, 28, 30, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 88, 90, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 84, 86, 86, 88, 90, 92, 92, 94, 30, 32, 32, 34, 24, 26, 26, 28, 18, 20, 20, 22, 10, 12, 12, 14, 14, 16, 16, 18, 4, 6, 6, 8], "width": 151, "height": 184}}, "body_outline": {"body_outline": {"type": "mesh", "uvs": [0.78668, 0.0101, 0.86109, 0.04108, 0.9213, 0.09175, 0.95442, 0.14633, 0.97735, 0.21278, 0.98872, 0.28055, 0.98799, 0.33134, 0.98878, 0.4033, 0.98878, 0.46989, 0.9757, 0.56226, 0.96187, 0.6277, 0.93676, 0.70323, 0.90749, 0.76751, 0.87432, 0.81822, 0.81532, 0.88607, 0.73523, 0.9397, 0.65082, 0.9721, 0.54344, 0.99002, 0.41583, 0.99147, 0.29227, 0.97205, 0.21577, 0.937, 0.15294, 0.88953, 0.12353, 0.82976, 0.12501, 0.76767, 0.14717, 0.71821, 0.17823, 0.67192, 0.18282, 0.62259, 0.15907, 0.55727, 0.12793, 0.49536, 0.09321, 0.43271, 0.05971, 0.37784, 0.02622, 0.32298, 0.00524, 0.26576, 0.01326, 0.19552, 0.04879, 0.13692, 0.11257, 0.09711, 0.19336, 0.08299, 0.29997, 0.08034, 0.41314, 0.07495, 0.51813, 0.04719, 0.62641, 0.00987, 0.59248, 0.34584, 0.56159, 0.67472, 0.2459, 0.27138, 0.33797, 0.80521], "triangles": [43, 41, 27, 38, 41, 43, 38, 43, 37, 9, 42, 41, 41, 26, 27, 9, 41, 8, 27, 28, 43, 28, 29, 43, 8, 41, 7, 29, 30, 43, 41, 6, 7, 30, 31, 43, 6, 41, 5, 41, 3, 4, 41, 2, 3, 41, 1, 2, 41, 0, 1, 41, 40, 0, 38, 39, 41, 41, 39, 40, 5, 41, 4, 43, 33, 34, 34, 35, 43, 35, 36, 43, 31, 32, 43, 33, 43, 32, 43, 36, 37, 17, 18, 44, 16, 17, 44, 42, 15, 16, 19, 44, 18, 19, 20, 44, 16, 44, 42, 15, 42, 14, 20, 21, 44, 21, 22, 44, 14, 42, 13, 22, 23, 44, 13, 42, 12, 23, 24, 44, 24, 25, 44, 44, 25, 42, 12, 42, 11, 11, 42, 10, 25, 26, 42, 42, 26, 41, 10, 42, 9], "vertices": [1, 4, 96.2, -58.23, 1, 1, 4, 87.47, -68.71, 1, 2, 3, 167.23, -27.86, 3e-05, 4, 75.5, -76.04, 0.99997, 2, 3, 158.52, -36.19, 0.00174, 4, 63.79, -78.87, 0.99826, 2, 3, 147.09, -43.6, 0.01113, 4, 50.18, -79.54, 0.98887, 2, 3, 134.86, -49.27, 0.03503, 4, 36.76, -78.31, 0.96497, 2, 3, 125.26, -52.06, 0.06832, 4, 27.05, -75.9, 0.93168, 2, 3, 111.73, -56.31, 0.1509, 4, 13.22, -72.79, 0.8491, 2, 3, 99.18, -60.13, 0.26331, 4, 0.45, -69.79, 0.73669, 2, 3, 81.15, -63.38, 0.47183, 4, -16.78, -63.54, 0.52817, 2, 3, 68.16, -64.96, 0.63321, 4, -28.81, -58.39, 0.36679, 2, 3, 52.72, -65.35, 0.80028, 4, -42.36, -50.97, 0.19972, 2, 3, 39.21, -64.44, 0.90404, 4, -53.58, -43.41, 0.09596, 2, 3, 28.07, -62.14, 0.95667, 4, -62.07, -35.83, 0.04333, 2, 3, 12.46, -56.78, 0.99272, 4, -72.87, -23.35, 0.00728, 2, 3, -1.47, -47.29, 0.99998, 4, -80.15, -8.15, 2e-05, 1, 3, -11.6, -35.9, 1, 1, 3, -20.11, -20.08, 1, 1, 3, -26.47, -0.14, 1, 1, 3, -28.71, 20.36, 1, 2, 3, -25.75, 34.37, 0.99823, 4, -60.16, 74.67, 0.00177, 2, 3, -19.8, 46.95, 0.99018, 4, -48.7, 82.56, 0.00982, 2, 3, -9.94, 54.99, 0.97451, 4, -36.13, 84.56, 0.02549, 2, 3, 1.83, 58.32, 0.94598, 4, -24.28, 81.53, 0.05402, 2, 3, 12.21, 57.68, 0.90159, 4, -15.62, 75.76, 0.09841, 2, 3, 22.42, 55.46, 0.80672, 4, -7.91, 68.72, 0.19328, 2, 3, 31.93, 57.57, 0.65344, 4, 1.38, 65.77, 0.34656, 2, 3, 43.11, 65.04, 0.43307, 4, 14.79, 66.62, 0.56693, 2, 3, 53.29, 73.48, 0.26313, 4, 27.83, 68.8, 0.73687, 2, 3, 63.44, 82.52, 0.14376, 4, 41.15, 71.52, 0.85624, 2, 3, 72.19, 90.92, 0.07748, 4, 52.93, 74.4, 0.92252, 2, 3, 80.93, 99.32, 0.03938, 4, 64.71, 77.28, 0.96062, 2, 3, 90.71, 105.89, 0.01995, 4, 76.47, 78.05, 0.98005, 2, 3, 104.33, 108.66, 0.00747, 4, 89.64, 73.6, 0.99253, 2, 3, 117.07, 106.44, 0.00201, 4, 99.54, 65.29, 0.99799, 2, 3, 127.62, 98.72, 0.0001, 4, 104.79, 53.32, 0.9999, 1, 4, 104.47, 39.78, 1, 1, 4, 100.98, 22.64, 1, 1, 4, 97.77, 4.33, 1, 1, 4, 99.16, -13.68, 1, 1, 4, 102.26, -32.65, 1, 1, 4, 39.09, -12.1, 1, 1, 3, 40.19, -4.85, 1, 2, 3, 101.14, 67.81, 0.02226, 4, 66.37, 39.88, 0.97774, 2, 3, 4.92, 22.76, 0.9815, 4, -39.46, 49.22, 0.0185], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 42, 44, 44, 46, 50, 52, 56, 58, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 52, 54, 54, 56, 58, 60, 60, 62, 10, 12, 12, 14, 14, 16, 76, 78, 78, 80, 72, 74, 74, 76, 46, 48, 48, 50, 38, 40, 40, 42, 30, 32, 32, 34, 16, 18, 18, 20, 20, 22, 22, 24, 6, 8, 8, 10, 50, 84, 16, 82, 10, 82], "width": 164, "height": 197}}, "eye": {"eye": {"x": 2.66, "y": 1.99, "rotation": -6.5, "width": 59, "height": 64}}, "eyebrow": {"eyebrow": {"x": 0.01, "y": 1.53, "rotation": -103.22, "width": 40, "height": 19}}, "eyebrow_base": {"eyebrow_base": {"type": "mesh", "uvs": [0.85823, 0.06358, 0.9559, 0.19185, 0.99177, 0.3406, 0.96587, 0.50732, 0.89446, 0.66374, 0.78923, 0.78689, 0.67989, 0.85362, 0.57056, 0.92034, 0.41481, 0.9592, 0.25907, 0.99806, 0.13622, 0.97607, 0.05554, 0.92323, 0.01164, 0.82361, 0.01271, 0.69821, 0.03067, 0.57687, 0.08947, 0.41313, 0.22286, 0.21627, 0.44255, 0.05604, 0.61169, 0.01788, 0.76544, 0.01786], "triangles": [4, 5, 18, 6, 7, 17, 8, 9, 14, 10, 14, 9, 8, 16, 7, 14, 10, 13, 11, 12, 13, 13, 10, 11, 8, 14, 15, 5, 6, 18, 3, 4, 19, 8, 15, 16, 17, 18, 6, 19, 4, 18, 3, 0, 1, 0, 3, 19, 3, 1, 2, 7, 16, 17], "vertices": [2, 4, 64.71, -19.51, 0.95164, 9, 34.54, -45.13, 0.04836, 2, 4, 56.04, -25.59, 0.85041, 9, 25.86, -51.21, 0.14959, 2, 4, 47.41, -26.55, 0.78028, 9, 17.23, -52.17, 0.21972, 2, 4, 38.96, -22.41, 0.46133, 9, 8.79, -48.03, 0.53867, 2, 4, 31.91, -14.82, 0.15018, 9, 1.73, -40.44, 0.84982, 2, 4, 27.26, -4.97, 0.00234, 9, -2.91, -30.59, 0.99766, 2, 4, 25.71, 4.49, 0.02997, 9, -4.46, -21.13, 0.97003, 1, 9, -6.01, -11.67, 1, 1, 9, -5.21, 1.1, 1, 1, 9, -4.4, 13.87, 1, 2, 4, 29.23, 48.9, 0.05319, 9, -0.95, 23.28, 0.94681, 2, 4, 33.55, 54.6, 0.12427, 9, 3.37, 28.98, 0.87573, 2, 4, 39.7, 56.81, 0.21206, 9, 9.52, 31.19, 0.78794, 2, 4, 46.39, 55.15, 0.40153, 9, 16.21, 29.53, 0.59847, 2, 4, 52.55, 52.21, 0.56708, 9, 22.38, 26.59, 0.43292, 2, 4, 60.23, 45.51, 0.85072, 9, 30.06, 19.89, 0.14928, 2, 4, 68.3, 32.52, 0.96976, 9, 38.13, 6.9, 0.03024, 1, 4, 72.81, 13.18, 1, 1, 4, 71.73, -0.64, 1, 1, 4, 68.88, -12.76, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 20, 22, 24, 26, 10, 12, 12, 14, 14, 16, 16, 18], "width": 81, "height": 55}}, "mouth": {"mouth": {"type": "mesh", "uvs": [0.02798, 0.45642, 0.2224, 0.48697, 0.41682, 0.51752, 0.60591, 0.69267, 0.79501, 0.86782, 0.94416, 1, 1, 0.80672, 0.98322, 0.52974, 0.82874, 0.35866, 0.67427, 0.18758, 0.46565, 0.09379, 0.25702, 0, 0.02841, 0.0611], "triangles": [9, 2, 10, 3, 9, 8, 3, 2, 9, 4, 3, 8, 4, 8, 7, 4, 7, 6, 5, 4, 6, 1, 12, 11, 2, 1, 11, 0, 12, 1, 10, 2, 11], "vertices": [2, 12, 13.14, 2.02, 0.97305, 13, -19.16, -14.48, 0.02695, 2, 12, 5.62, 3.12, 0.84029, 13, -12.67, -10.52, 0.15971, 2, 12, -1.9, 4.23, 0.59642, 13, -6.18, -6.57, 0.40358, 2, 12, -9.03, 7.77, 0.29534, 13, 1.56, -4.74, 0.70466, 2, 12, -16.15, 11.3, 0.09477, 13, 9.3, -2.91, 0.90523, 2, 12, -21.77, 13.99, 0.0053, 13, 15.35, -1.39, 0.9947, 1, 13, 15.23, 2.55, 1, 2, 12, -23.91, 6.14, 0.0026, 13, 11.98, 6.02, 0.9974, 2, 12, -18.13, 2.78, 0.08639, 13, 5.38, 4.92, 0.91361, 2, 12, -12.35, -0.59, 0.2813, 13, -1.22, 3.81, 0.7187, 2, 12, -4.36, -2.81, 0.58212, 13, -8.78, 0.41, 0.41788, 2, 12, 3.63, -5.03, 0.83166, 13, -16.35, -2.98, 0.16834, 2, 12, 12.6, -4.68, 0.97009, 13, -23.02, -8.98, 0.02991], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 12, 14, 24, 0], "width": 39, "height": 17}}, "mouth_base": {"mouth_base": {"type": "mesh", "uvs": [0.48714, 0.01563, 0.67579, 0.09868, 0.83764, 0.24122, 0.99276, 0.51641, 0.98891, 0.8523, 0.81241, 0.99438, 0.70697, 0.8951, 0.57275, 0.71096, 0.38341, 0.6312, 0.20053, 0.62522, 0.0255, 0.62112, 0.02657, 0.27479, 0.13787, 0.14853, 0.28841, 0.02039], "triangles": [7, 0, 1, 7, 1, 2, 6, 7, 2, 2, 3, 6, 4, 6, 3, 5, 6, 4, 9, 12, 13, 11, 12, 9, 10, 11, 9, 8, 13, 0, 9, 13, 8, 8, 0, 7], "vertices": [2, 12, -5.34, -10.65, 0.55421, 13, -13.03, 7.07, 0.44579, 2, 12, -19.16, -5.83, 0.28999, 13, 0.68, 12.19, 0.71001, 2, 12, -30.77, 1.51, 0.10977, 13, 14.29, 13.97, 0.89023, 2, 12, -41.41, 14.75, 0.02711, 13, 30.94, 10.57, 0.97289, 2, 12, -39.95, 29.8, 0.02375, 13, 39.43, -1.93, 0.97625, 2, 12, -26.26, 35.15, 0.08, 13, 32.32, -14.8, 0.92, 2, 12, -18.72, 30.09, 0.22486, 13, 23.28, -15.72, 0.77514, 2, 12, -9.33, 21.05, 0.43225, 13, 10.28, -14.76, 0.56775, 2, 12, 4.55, 16.37, 0.65861, 13, -3.39, -20.03, 0.34139, 2, 12, 18.21, 15.04, 0.83244, 13, -14.74, -27.74, 0.16756, 2, 12, 31.28, 13.85, 0.94142, 13, -25.56, -35.17, 0.05858, 2, 12, 30, -1.69, 0.98535, 13, -34.5, -22.4, 0.01465, 2, 12, 21.24, -6.71, 0.95122, 13, -30.96, -12.94, 0.04878, 2, 12, 9.53, -11.58, 0.80488, 13, -25.08, -1.71, 0.19512], "hull": 14, "edges": [6, 8, 8, 10, 20, 22, 2, 0, 0, 26, 2, 4, 4, 6, 22, 24, 24, 26, 14, 16, 10, 12, 12, 14, 16, 18, 18, 20], "width": 75, "height": 45}}, "mouth_outline": {"mouth_outline": {"type": "mesh", "uvs": [0.47403, 0.01652, 0.64537, 0.06762, 0.82038, 0.19972, 0.92118, 0.33064, 0.98946, 0.49133, 1, 0.67955, 0.90303, 1, 0.78898, 1, 0.65164, 0.93409, 0.52941, 0.76751, 0.35057, 0.69814, 0.19391, 0.72817, 0.04908, 0.66202, 0, 0.44521, 0.04483, 0.26478, 0.17327, 0.10478, 0.31495, 0.01388], "triangles": [9, 0, 1, 9, 1, 2, 9, 2, 3, 8, 9, 3, 4, 8, 3, 10, 0, 9, 5, 8, 4, 7, 8, 5, 6, 7, 5, 13, 14, 11, 12, 13, 11, 10, 16, 0, 15, 16, 10, 15, 11, 14, 15, 10, 11], "vertices": [2, 12, -4.03, -17.03, 0.52817, 13, -17.29, 11.91, 0.47183, 2, 12, -19.37, -12.3, 0.20432, 13, -3.59, 17.5, 0.79568, 2, 12, -33.43, -5.44, 0.03298, 13, 13.28, 19.93, 0.96702, 2, 12, -42.02, 2.17, 0.01413, 13, 24.71, 18.87, 0.98587, 2, 12, -47.18, 14.02, 0.00471, 13, 34.83, 14.83, 0.99529, 2, 12, -47.2, 24.73, 1e-05, 13, 41.77, 6.6, 0.99999, 2, 12, -37.38, 42.28, 0.00226, 13, 45.43, -13.18, 0.99774, 2, 12, -27.48, 41.52, 0.08433, 13, 37.33, -18.91, 0.91567, 2, 12, -17.72, 35.66, 0.20407, 13, 24.9, -22.51, 0.79593, 2, 12, -7.36, 25.62, 0.35926, 13, 10.61, -20.53, 0.64074, 2, 12, 8.05, 20.93, 0.54517, 13, -4.6, -25.86, 0.45483, 2, 12, 22.47, 21.8, 0.75807, 13, -15.72, -35.08, 0.24193, 2, 12, 35.21, 17.34, 0.93397, 13, -26.35, -40.36, 0.06603, 2, 12, 38.51, 4.69, 0.99932, 13, -36.98, -32.74, 0.00068, 2, 12, 33.83, -5.27, 0.99997, 13, -39.4, -22.15, 3e-05, 2, 12, 21.98, -13.51, 0.99991, 13, -36.19, -6.31, 9e-05, 2, 12, 9.43, -17.89, 0.84263, 13, -28.83, 2.87, 0.15737], "hull": 17, "edges": [8, 10, 10, 12, 12, 14, 24, 26, 26, 28, 22, 24, 14, 16, 20, 22, 16, 18, 18, 20, 2, 0, 0, 32, 28, 30, 30, 32, 2, 4, 4, 6, 6, 8], "width": 87, "height": 57}}, "pupil": {"pupil": {"width": 13, "height": 19}}}}], "animations": {".wip_Test": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}, {"time": 12.6333, "name": "Arm_L"}, {"time": 12.9333}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}, {"time": 12.6333, "name": "ARM_L_outline"}, {"time": 12.9333}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}, {"time": 12.6333, "name": "Arm_R"}, {"time": 12.9333}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}, {"time": 12.6333, "name": "ARM_R_outline"}, {"time": 12.9333}]}, "blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 12.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 13.1333, "color": "ffffffff"}, {"time": 13.6333, "color": "ffffff00"}], "attachment": [{}, {"time": 12.6333}, {"time": 12.9333, "name": "blot"}]}, "blot_drop2": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop2"}, {"time": 13.1333}]}, "blot_drop3": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop2"}, {"time": 13.2333}]}, "blot_drop4": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop2"}, {"time": 13.2}]}, "blot_drop5": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop1"}, {"time": 13.1333}]}, "blot_drop6": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop2"}, {"time": 13.2}]}, "blot_drop7": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop2"}, {"time": 13.2667}]}, "blot_drop8": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9333, "name": "blot_drop1"}, {"time": 13.3}]}, "blot_drop_s1": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop1"}, {"time": 13.1}]}, "blot_drop_s2": {"attachment": [{}, {"time": 12.6333}, {"time": 12.9, "name": "blot_drop1"}, {"time": 13.0667}]}, "body": {"attachment": [{"name": "body"}, {"time": 12.6333, "name": "body"}, {"time": 12.9333}]}, "body_outline": {"attachment": [{"name": "body_outline"}, {"time": 12.6333, "name": "body_outline"}, {"time": 12.9333}]}, "eye": {"attachment": [{"name": "eye"}, {"time": 12.6333, "name": "eye"}, {"time": 12.9333}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}, {"time": 12.6333, "name": "eyebrow"}, {"time": 12.9333}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}, {"time": 12.6333, "name": "eyebrow_base"}, {"time": 12.9333}]}, "mouth": {"attachment": [{"name": "mouth"}, {"time": 12.6333, "name": "mouth"}, {"time": 12.9333}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}, {"time": 12.6333, "name": "mouth_base"}, {"time": 12.9333}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}, {"time": 12.6333, "name": "mouth_outline"}, {"time": 12.9333}]}, "pupil": {"attachment": [{"name": "pupil"}, {"time": 12.6333, "name": "pupil"}, {"time": 12.9333}]}}, "bones": {"pupil": {"translate": [{"x": 12.53, "y": 2.3, "curve": "stepped"}, {"time": 0.3, "x": 12.53, "y": 2.3, "curve": [0.754, 15.3, 2.573, 16.67, 0.755, 5.16, 2.573, 5.11]}, {"time": 3.0333, "x": 15.01, "y": 3.56, "curve": [3.153, 14.58, 3.257, -10.5, 3.156, 3.15, 3.278, 3.7]}, {"time": 3.4, "x": -11.48, "y": 3.7, "curve": [3.604, -12.89, 5.36, -13.33, 3.608, 3.7, 5.359, 3.8]}, {"time": 5.5667, "x": -12.34, "y": 3.8, "curve": [5.711, -11.66, 5.856, 1.86, 5.711, 3.8, 5.856, 0.17]}, {"time": 6, "x": 1.86, "y": 0.17, "curve": [6.022, 1.86, 6.044, 5.72, 6.022, 0.17, 6.044, -19.17]}, {"time": 6.0667, "x": 5.72, "y": -19.17, "curve": "stepped"}, {"time": 6.2333, "x": 5.72, "y": -19.17, "curve": [6.367, 5.72, 6.5, 4.69, 6.367, -19.17, 6.5, -23.09]}, {"time": 6.6333, "x": 4.69, "y": -23.09, "curve": "stepped"}, {"time": 12.6333, "x": 4.69, "y": -23.09, "curve": [12.689, 4.69, 12.744, 2, 12.689, -23.09, 12.744, -4.78]}, {"time": 12.8, "x": 2, "y": -4.78}], "scale": [{"curve": "stepped"}, {"time": 6, "curve": [6.017, 1.102, 6.05, 0.559, 6.017, 1.102, 6.05, 0.559]}, {"time": 6.0667, "x": 0.559, "y": 0.559, "curve": [6.085, 0.559, 6.215, 0.886, 6.085, 0.559, 6.215, 0.886]}, {"time": 6.2333, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 12.6333}]}, "Eyelid": {"rotate": [{"curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 12.6333}], "translate": [{"curve": "stepped"}, {"time": 6, "curve": [6.022, 0, 6.046, 21.34, 6.022, 0, 6.044, 1.23]}, {"time": 6.0667, "x": 21.34, "y": 1.23, "curve": [6.086, 21.34, 6.214, 22.05, 6.086, 1.23, 6.214, 0.13]}, {"time": 6.2333, "x": 21.34, "y": 1.23, "curve": [6.366, 16.54, 6.5, -14.63, 6.365, 8.64, 6.5, -1.18]}, {"time": 6.6333, "x": -14.63, "y": -1.18, "curve": [8.633, -14.63, 10.633, -23.56, 8.633, -1.18, 10.633, -3.85]}, {"time": 12.6333, "x": -23.56, "y": -3.85, "curve": [12.644, -23.56, 12.656, -36.95, 12.644, -3.85, 12.656, -7.84]}, {"time": 12.6667, "x": -36.95, "y": -7.84, "curve": [12.711, -36.95, 12.756, 33.2, 12.711, -7.84, 12.756, -11.22]}, {"time": 12.8, "x": 33.2, "y": -11.22}]}, "mouth_base": {"rotate": [{"curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 12.6333}], "translate": [{"x": 1.63, "y": 0.44, "curve": [0.034, 1, 0.067, 0.36, 0.034, 0.2, 0.067, 0]}, {"time": 0.1, "curve": [0.383, -3.11, 2.597, -3.11, 0.403, 0, 2.597, 0]}, {"time": 2.9, "x": -3.11, "curve": [3, -3.11, 3.1, 2.58, 3, 0, 3.1, 1.38]}, {"time": 3.2, "x": 2.58, "y": 1.38, "curve": "stepped"}, {"time": 3.2667, "x": 2.58, "y": 1.38, "curve": [3.3, 2.58, 3.533, 0, 3.3, 1.38, 3.533, 0]}, {"time": 3.6667, "curve": [4.218, 0, 4.217, 2.58, 4.218, 0, 4.217, 1.38]}, {"time": 4.3, "x": 2.58, "y": 1.38}, {"time": 5.4333, "curve": "stepped"}, {"time": 5.5, "curve": [5.589, 0, 5.678, 3.78, 5.589, 0, 5.678, 1.38]}, {"time": 5.7667, "x": 3.78, "y": 1.38, "curve": "stepped"}, {"time": 5.8333, "x": 3.78, "y": 1.38, "curve": [5.889, 3.78, 5.944, 1.63, 5.889, 1.38, 5.944, 0.44]}, {"time": 6, "x": 1.63, "y": 0.44, "curve": [6.022, 1.63, 6.044, 5.98, 6.022, 0.44, 6.044, 0.44]}, {"time": 6.0667, "x": 5.98, "y": 0.44, "curve": "stepped"}, {"time": 6.2333, "x": 5.98, "y": 0.44, "curve": [6.367, 5.98, 6.5, -3.11, 6.367, 0.44, 6.5, 0]}, {"time": 6.6333, "x": -3.11, "curve": "stepped"}, {"time": 12.6333, "x": -3.11}], "scale": [{"y": 0.973, "curve": [0.034, 1, 0.067, 1, 0.034, 0.988, 0.067, 1]}, {"time": 0.1, "curve": [0.403, 1, 2.597, 1, 0.403, 1, 2.597, 1.021]}, {"time": 2.9, "y": 1.021, "curve": [3, 1, 3.1, 1, 3, 1.021, 3.1, 0.951]}, {"time": 3.2, "y": 0.951, "curve": "stepped"}, {"time": 3.2667, "y": 0.951, "curve": [3.4, 1, 3.533, 1, 3.4, 0.951, 3.533, 1]}, {"time": 3.6667, "curve": [4.218, 1, 4.217, 1, 4.218, 1, 4.217, 0.951]}, {"time": 4.3, "y": 0.951, "curve": [4.329, 1, 4.753, 1, 4.329, 0.951, 4.753, 1]}, {"time": 5.4333, "curve": "stepped"}, {"time": 5.5, "curve": [5.589, 1, 5.678, 1, 5.589, 1, 5.678, 0.916]}, {"time": 5.7667, "y": 0.916, "curve": "stepped"}, {"time": 5.8333, "y": 0.916, "curve": [5.889, 1, 5.944, 1, 5.889, 0.916, 5.944, 0.973]}, {"time": 6, "y": 0.973, "curve": "stepped"}, {"time": 6.2333, "y": 0.973, "curve": [6.367, 1, 6.5, 1, 6.367, 0.973, 6.5, 1.021]}, {"time": 6.6333, "y": 1.021, "curve": "stepped"}, {"time": 12.6333, "y": 1.021}, {"time": 12.8, "x": 1.284, "y": 1.311}]}, "eye": {"rotate": [{"curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.6333, "curve": "stepped"}, {"time": 12.6333}], "translate": [{"curve": "stepped"}, {"time": 6, "curve": "stepped"}, {"time": 6.6333}, {"time": 12.6333, "y": -5.55}], "scale": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.8, "x": 1.358, "y": 1.358}]}, "Face": {"translate": [{"y": -5.55, "curve": "stepped"}, {"time": 3.0333, "y": -5.55, "curve": [3.156, 0, 3.278, 0, 3.156, -5.55, 3.278, 5.12]}, {"time": 3.4, "y": 5.12, "curve": "stepped"}, {"time": 5.5667, "y": 5.12, "curve": [5.711, 0, 5.856, 0, 5.711, 5.12, 5.856, -5.55]}, {"time": 6, "y": -5.55, "curve": [6.022, 0, 6.044, 6.62, 6.022, -5.55, 6.044, -5.55]}, {"time": 6.0667, "x": 6.62, "y": -5.55, "curve": "stepped"}, {"time": 6.2333, "x": 6.62, "y": -5.55, "curve": [6.367, 6.62, 6.5, 0, 6.367, -5.55, 6.5, -5.55]}, {"time": 6.6333, "y": -5.55, "curve": "stepped"}, {"time": 12.6333, "y": -5.55}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 3.0333, "x": 1.22, "y": -5.15, "curve": [3.156, 1.22, 3.278, 0.98, 3.156, -5.15, 3.278, 3.01]}, {"time": 3.4, "x": 0.98, "y": 3.01, "curve": "stepped"}, {"time": 5.6, "x": 0.98, "y": 3.01, "curve": [5.722, 0.98, 5.844, 1.22, 5.722, 3.01, 5.844, -5.15]}, {"time": 5.9667, "x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 6, "x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 6.6333, "x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 12.6333, "x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, 8.94]}, {"time": 1, "y": 8.94, "curve": [1.333, 0, 1.667, 0, 1.333, 8.94, 1.667, 0]}, {"time": 2, "curve": [2.333, 0, 2.667, 0, 2.333, 0, 2.667, 8.94]}, {"time": 3, "y": 8.94, "curve": [3.333, 0, 3.667, 0, 3.333, 8.94, 3.667, 0]}, {"time": 4, "curve": [4.333, 0, 4.667, 0, 4.333, 0, 4.667, 8.94]}, {"time": 5, "y": 8.94, "curve": [5.333, 0, 5.667, 0, 5.333, 8.94, 5.667, 0]}, {"time": 6, "curve": [6.022, 0, 6.044, 0, 6.022, 0, 6.045, 21.24]}, {"time": 6.0667, "y": 26.48, "curve": [6.083, 0, 6.223, 0, 6.125, 40.54, 6.172, 32.29]}, {"time": 6.2333, "y": 26.48, "curve": [6.367, 0, 6.5, 0, 6.366, 13.87, 6.5, 0]}, {"time": 6.6333, "curve": [6.967, 0, 7.3, 0, 6.967, 0, 7.3, 8.94]}, {"time": 7.6333, "y": 8.94, "curve": [7.967, 0, 8.3, 0, 7.967, 8.94, 8.3, 0]}, {"time": 8.6333, "curve": [8.967, 0, 9.3, 0, 8.967, 0, 9.3, 8.94]}, {"time": 9.6333, "y": 8.94, "curve": [9.967, 0, 10.3, 0, 9.967, 8.94, 10.3, 0]}, {"time": 10.6333, "curve": [10.967, 0, 11.3, 0, 10.967, 0, 11.3, 8.94]}, {"time": 11.6333, "y": 8.94, "curve": [11.967, 0, 12.3, 2.73, 11.967, 8.94, 12.3, 0.55]}, {"time": 12.6333, "x": 2.73, "y": 0.55, "curve": [12.644, 2.73, 12.656, 6.82, 12.644, 0.55, 12.656, 1.36]}, {"time": 12.6667, "x": 6.82, "y": 1.36}], "scale": [{"x": 1.009, "y": 0.991, "curve": [0.136, 1.02, 0.268, 1.03, 0.136, 0.98, 0.268, 0.97]}, {"time": 0.4, "x": 1.03, "y": 0.97, "curve": [0.733, 1.03, 1.067, 0.97, 0.733, 0.97, 1.067, 1.03]}, {"time": 1.4, "x": 0.97, "y": 1.03, "curve": [1.601, 0.97, 1.8, 0.992, 1.601, 1.03, 1.8, 1.008]}, {"time": 2, "x": 1.009, "y": 0.991, "curve": [2.136, 1.02, 2.268, 1.03, 2.136, 0.98, 2.268, 0.97]}, {"time": 2.4, "x": 1.03, "y": 0.97, "curve": [2.733, 1.03, 3.067, 0.97, 2.733, 0.97, 3.067, 1.03]}, {"time": 3.4, "x": 0.97, "y": 1.03, "curve": [3.601, 0.97, 3.8, 0.992, 3.601, 1.03, 3.8, 1.008]}, {"time": 4, "x": 1.009, "y": 0.991, "curve": [4.136, 1.02, 4.268, 1.03, 4.136, 0.98, 4.268, 0.97]}, {"time": 4.4, "x": 1.03, "y": 0.97, "curve": [4.733, 1.03, 5.067, 0.97, 4.733, 0.97, 5.067, 1.03]}, {"time": 5.4, "x": 0.97, "y": 1.03, "curve": [5.601, 0.97, 5.8, 1.009, 5.601, 1.03, 5.8, 0.991]}, {"time": 6, "x": 1.009, "y": 0.991, "curve": [6.022, 1.009, 6.044, 0.913, 6.022, 0.991, 6.044, 1.083]}, {"time": 6.0667, "x": 0.9, "y": 1.1, "curve": [6.127, 0.865, 6.175, 0.881, 6.129, 1.148, 6.175, 1.12]}, {"time": 6.2333, "x": 0.9, "y": 1.1, "curve": [6.367, 0.944, 6.5, 0.998, 6.367, 1.055, 6.5, 1.002]}, {"time": 6.6333, "x": 1.009, "y": 0.991, "curve": [6.769, 1.02, 6.901, 1.03, 6.769, 0.98, 6.901, 0.97]}, {"time": 7.0333, "x": 1.03, "y": 0.97, "curve": [7.367, 1.03, 7.7, 0.97, 7.367, 0.97, 7.7, 1.03]}, {"time": 8.0333, "x": 0.97, "y": 1.03, "curve": [8.234, 0.97, 8.433, 0.992, 8.234, 1.03, 8.433, 1.008]}, {"time": 8.6333, "x": 1.009, "y": 0.991, "curve": [8.769, 1.02, 8.901, 1.03, 8.769, 0.98, 8.901, 0.97]}, {"time": 9.0333, "x": 1.03, "y": 0.97, "curve": [9.367, 1.03, 9.7, 0.97, 9.367, 0.97, 9.7, 1.03]}, {"time": 10.0333, "x": 0.97, "y": 1.03, "curve": [10.234, 0.97, 10.433, 0.992, 10.234, 1.03, 10.433, 1.008]}, {"time": 10.6333, "x": 1.009, "y": 0.991, "curve": [10.769, 1.02, 10.901, 1.03, 10.769, 0.98, 10.901, 0.97]}, {"time": 11.0333, "x": 1.03, "y": 0.97, "curve": [11.367, 1.03, 11.7, 0.97, 11.367, 0.97, 11.7, 1.03]}, {"time": 12.0333, "x": 0.97, "y": 1.03, "curve": [12.234, 0.97, 12.433, 1.009, 12.234, 1.03, 12.433, 0.991]}, {"time": 12.6333, "x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 1.32, "curve": [0.224, 2.81, 0.446, 6.72]}, {"time": 0.6667, "value": 6.72, "curve": [1, 6.72, 1.333, 0]}, {"time": 1.6667, "curve": [1.779, 0, 1.889, 0.58]}, {"time": 2, "value": 1.32, "curve": [2.224, 2.81, 2.446, 6.72]}, {"time": 2.6667, "value": 6.72, "curve": [3, 6.72, 3.333, 0]}, {"time": 3.6667, "curve": [3.779, 0, 3.889, 0.58]}, {"time": 4, "value": 1.32, "curve": [4.224, 2.81, 4.446, 6.72]}, {"time": 4.6667, "value": 6.72, "curve": [5, 6.72, 5.333, 0]}, {"time": 5.6667, "curve": [5.779, 0, 5.889, 1.32]}, {"time": 6, "value": 1.32, "curve": [6.022, 1.32, 6.044, -16.51]}, {"time": 6.0667, "value": -16.51, "curve": "stepped"}, {"time": 6.2333, "value": -16.51, "curve": [6.367, -16.51, 6.5, 4.79]}, {"time": 6.6333, "value": 5.68, "curve": [6.858, 7.17, 7.079, 11.08]}, {"time": 7.3, "value": 11.08, "curve": [7.633, 11.08, 7.967, 4.36]}, {"time": 8.3, "value": 4.36, "curve": [8.412, 4.36, 8.522, 4.94]}, {"time": 8.6333, "value": 5.68, "curve": [8.858, 7.17, 9.079, 11.08]}, {"time": 9.3, "value": 11.08, "curve": [9.633, 11.08, 9.967, 4.36]}, {"time": 10.3, "value": 4.36, "curve": [10.412, 4.36, 10.522, 4.94]}, {"time": 10.6333, "value": 5.68, "curve": [10.858, 7.17, 11.079, 11.08]}, {"time": 11.3, "value": 11.08, "curve": [11.633, 11.08, 11.967, 4.36]}, {"time": 12.3, "value": 4.36, "curve": [12.412, 4.36, 12.522, 15.11]}, {"time": 12.6333, "value": 15.11, "curve": [12.644, 15.11, 12.656, 29.24]}, {"time": 12.6667, "value": 29.24, "curve": [12.711, 29.24, 12.756, -25.37]}, {"time": 12.8, "value": -25.37}]}, "body": {"rotate": [{"curve": "stepped"}, {"time": 6, "curve": [6.022, 0, 6.044, 5.97]}, {"time": 6.0667, "value": 5.97, "curve": "stepped"}, {"time": 6.2333, "value": 5.97, "curve": [6.367, 5.97, 6.5, 5.76]}, {"time": 6.6333, "value": 5.76, "curve": "stepped"}, {"time": 12.6333, "value": 5.76, "curve": [12.689, 5.76, 12.744, 21.27]}, {"time": 12.8, "value": 21.27}]}, "Arm_R": {"rotate": [{"value": -1.38, "curve": [0.322, -1.38, 0.644, 6.43]}, {"time": 0.9667, "value": 6.43, "curve": [1.311, 6.43, 1.656, -1.38]}, {"time": 2, "value": -1.38, "curve": [2.322, -1.38, 2.644, 6.43]}, {"time": 2.9667, "value": 6.43, "curve": [3.311, 6.43, 3.656, -1.38]}, {"time": 4, "value": -1.38, "curve": [4.322, -1.38, 4.644, 6.43]}, {"time": 4.9667, "value": 6.43, "curve": [5.311, 6.43, 5.656, -1.38]}, {"time": 6, "value": -1.38, "curve": [6.022, -1.38, 6.044, -17.96]}, {"time": 6.0667, "value": -17.96, "curve": "stepped"}, {"time": 6.2333, "value": -17.96, "curve": [6.367, -17.96, 6.5, 34.08]}, {"time": 6.6333, "value": 33.08, "curve": [6.79, 31.91, 6.945, 31.05]}, {"time": 7.1, "value": 31.05, "curve": [7.19, 31.05, 7.28, 31.35]}, {"time": 7.3667, "value": 31.81, "curve": [7.78, 33.94, 8.19, 39.83]}, {"time": 8.6, "value": 39.83, "curve": [9.1, 39.83, 9.6, 31.05]}, {"time": 10.1, "value": 31.05, "curve": [10.6, 31.05, 11.1, 39.83]}, {"time": 11.6, "value": 39.83, "curve": [11.878, 39.83, 12.157, 37.13]}, {"time": 12.4333, "value": 34.72, "curve": [12.5, 34.14, 12.567, 34.63]}, {"time": 12.6333, "value": 33.08, "curve": [12.656, 32.57, 12.644, 32.31]}, {"time": 12.6667, "value": 30.77, "curve": [12.678, 30, 12.689, -97.36]}, {"time": 12.7, "value": -104.9, "curve": [12.7, -105.15, 12.767, -62.26]}, {"time": 12.8, "value": -62.26}], "translate": [{"curve": "stepped"}, {"time": 6, "curve": [6.022, 0, 6.044, 5.01, 6.022, 0, 6.044, -0.53]}, {"time": 6.0667, "x": 5.01, "y": -0.53, "curve": "stepped"}, {"time": 6.2333, "x": 5.01, "y": -0.53, "curve": [6.367, 5.01, 6.5, 0, 6.367, -0.53, 6.5, 0]}, {"time": 6.6333, "curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.6667, "curve": [12.678, 0, 12.689, -29.33, 12.678, 0, 12.689, -11.97]}, {"time": 12.7, "x": -31.05, "y": -12.67, "curve": [12.708, -32.37, 12.767, 12.01, 12.702, -12.77, 12.767, -7.28]}, {"time": 12.8, "x": 12.01, "y": -7.28}]}, "Arm_R2": {"rotate": [{"value": -7.3, "curve": [0.079, -8.33, 0.156, -9.06]}, {"time": 0.2333, "value": -9.06, "curve": [0.556, -9.06, 0.878, 4.16]}, {"time": 1.2, "value": 4.16, "curve": [1.468, 4.16, 1.733, -3.87]}, {"time": 2, "value": -7.3, "curve": [2.079, -8.33, 2.156, -9.06]}, {"time": 2.2333, "value": -9.06, "curve": [2.556, -9.06, 2.878, 4.16]}, {"time": 3.2, "value": 4.16, "curve": [3.468, 4.16, 3.733, -3.87]}, {"time": 4, "value": -7.3, "curve": [4.079, -8.33, 4.156, -9.06]}, {"time": 4.2333, "value": -9.06, "curve": [4.556, -9.06, 4.878, 4.16]}, {"time": 5.2, "value": 4.16, "curve": [5.468, 4.16, 5.733, -7.3]}, {"time": 6, "value": -7.3, "curve": [6.022, -7.3, 6.044, -23.35]}, {"time": 6.0667, "value": -23.35, "curve": "stepped"}, {"time": 6.2333, "value": -23.35, "curve": [6.367, -23.35, 6.5, -2.2]}, {"time": 6.6333, "value": -3.26, "curve": [6.879, -5.21, 7.123, -7.13]}, {"time": 7.3667, "value": -7.13, "curve": [7.867, -7.13, 8.367, 0.88]}, {"time": 8.8667, "value": 0.88, "curve": [9.367, 0.88, 9.867, -7.13]}, {"time": 10.3667, "value": -7.13, "curve": [10.867, -7.13, 11.367, 0.88]}, {"time": 11.8667, "value": 0.88, "curve": [12.057, 0.88, 12.247, -0.26]}, {"time": 12.4333, "value": -1.7, "curve": [12.5, -2.2, 12.567, -64.62]}, {"time": 12.6333, "value": -64.62, "curve": [12.656, -64.62, 12.644, -154.98]}, {"time": 12.6667, "value": -154.98, "curve": [12.678, -154.98, 12.689, -13.83]}, {"time": 12.7, "value": -13.83}]}, "Arm_L": {"rotate": [{"value": 4.12, "curve": [0.322, 4.12, 0.644, -2.03]}, {"time": 0.9667, "value": -2.03, "curve": [1.311, -2.03, 1.656, 4.12]}, {"time": 2, "value": 4.12, "curve": [2.322, 4.12, 2.644, -2.03]}, {"time": 2.9667, "value": -2.03, "curve": [3.311, -2.03, 3.656, 4.12]}, {"time": 4, "value": 4.12, "curve": [4.322, 4.12, 4.644, -2.03]}, {"time": 4.9667, "value": -2.03, "curve": [5.311, -2.03, 5.656, 4.12]}, {"time": 6, "value": 4.12, "curve": [6.022, 4.12, 6.044, 12.29]}, {"time": 6.0667, "value": 12.29, "curve": "stepped"}, {"time": 6.2333, "value": 12.29, "curve": [6.367, 12.29, 6.5, 11.85]}, {"time": 6.6333, "value": 11.09, "curve": [6.947, 9.3, 7.257, 6.94]}, {"time": 7.5667, "value": 6.94, "curve": [8.067, 6.94, 8.567, 13.06]}, {"time": 9.0667, "value": 13.06, "curve": [9.567, 13.06, 10.067, 6.94]}, {"time": 10.5667, "value": 6.94, "curve": [11.067, 6.94, 11.567, 13.06]}, {"time": 12.0667, "value": 13.06, "curve": [12.257, 13.06, 12.444, -3.98]}, {"time": 12.6333, "value": -3.98, "curve": [12.644, -3.98, 12.656, -53.15]}, {"time": 12.6667, "value": -53.15, "curve": [12.701, 26.93, 12.756, 27.45]}, {"time": 12.8, "value": 27.45}]}, "Arm_L2": {"rotate": [{"value": 15.74, "curve": [0.113, 17.36, 0.223, 18.62]}, {"time": 0.3333, "value": 18.62, "curve": [0.656, 18.62, 0.978, 6.94]}, {"time": 1.3, "value": 6.94, "curve": [1.534, 6.94, 1.767, 12.37]}, {"time": 2, "value": 15.74, "curve": [2.113, 17.36, 2.223, 18.62]}, {"time": 2.3333, "value": 18.62, "curve": [2.656, 18.62, 2.978, 6.94]}, {"time": 3.3, "value": 6.94, "curve": [3.534, 6.94, 3.767, 12.37]}, {"time": 4, "value": 15.74, "curve": [4.113, 17.36, 4.223, 18.62]}, {"time": 4.3333, "value": 18.62, "curve": [4.656, 18.62, 4.978, 6.94]}, {"time": 5.3, "value": 6.94, "curve": [5.534, 6.94, 5.767, 15.74]}, {"time": 6, "value": 15.74, "curve": [6.022, 15.74, 6.044, 27.61]}, {"time": 6.0667, "value": 27.61, "curve": "stepped"}, {"time": 6.2333, "value": 27.61, "curve": [6.367, 27.61, 6.515, 33.14]}, {"time": 6.6333, "value": 30.38, "curve": [6.857, 25.16, 7.078, 20.42]}, {"time": 7.3, "value": 20.42, "curve": [7.39, 20.42, 7.48, 21.23]}, {"time": 7.5667, "value": 22.5, "curve": [7.98, 28.26, 8.39, 44.25]}, {"time": 8.8, "value": 44.25, "curve": [9.3, 44.25, 9.8, 20.42]}, {"time": 10.3, "value": 20.42, "curve": [10.8, 20.42, 11.3, 44.25]}, {"time": 11.8, "value": 44.25, "curve": [12.078, 44.25, 12.356, 3.54]}, {"time": 12.6333, "value": 3.54, "curve": [12.644, 3.54, 12.656, -36.7]}, {"time": 12.6667, "value": -36.7, "curve": [12.701, 20.59, 12.756, 23.61]}, {"time": 12.8, "value": 23.61}]}, "Mouth_control": {"rotate": [{"curve": "stepped"}, {"time": 6, "curve": [6.022, 0, 6.044, 19.58]}, {"time": 6.0667, "value": 19.58, "curve": "stepped"}, {"time": 6.2333, "value": 19.58, "curve": [6.367, 19.58, 6.5, -45.77]}, {"time": 6.6333, "value": -44.48, "curve": [6.756, -43.29, 6.878, -42.35]}, {"time": 7, "value": -42.35, "curve": [7.333, -42.35, 7.667, -49.3]}, {"time": 8, "value": -49.3, "curve": [8.333, -49.3, 8.667, -42.35]}, {"time": 9, "value": -42.35, "curve": [9.333, -42.35, 9.667, -49.3]}, {"time": 10, "value": -49.3, "curve": [10.333, -49.3, 10.667, -42.35]}, {"time": 11, "value": -42.35, "curve": [11.333, -42.35, 11.667, -49.3]}, {"time": 12, "value": -49.3, "curve": [12.211, -49.3, 12.422, -44.48]}, {"time": 12.6333, "value": -44.48}, {"time": 12.8, "value": -91.01}]}, "blot": {"translate": [{}, {"time": 12.6333, "x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 12.9667, "x": -12.21, "y": 15.4, "curve": [13.078, -12.21, 13.189, -12.22, 13.035, 8.18, 13.189, 1.44]}, {"time": 13.3, "x": -12.22, "y": 1.44}], "scale": [{}, {"time": 12.6333, "x": 0.7, "y": 0.7}, {"time": 12.9333, "x": 0.85, "y": 0.85, "curve": [12.96, 1.26, 13.111, 1.2, 12.96, 1.26, 13.111, 1.2]}, {"time": 13.2, "x": 1.2, "y": 1.2}]}, "cntr": {"translate": [{"curve": "stepped"}, {"time": 12.6333}], "scale": [{"curve": [0.022, 1, 8.389, 0.886, 0.022, 1, 8.389, 0.886]}, {"time": 12.6333, "x": 0.886, "y": 0.886, "curve": [12.644, 0.886, 12.656, 0.415, 12.644, 0.886, 12.656, 0.415]}, {"time": 12.6667, "x": 0.415, "y": 0.415, "curve": "stepped"}, {"time": 12.7, "x": 0.415, "y": 0.415, "curve": [12.718, 0.701, 12.772, 0.899, 12.721, 0.708, 12.772, 0.899]}, {"time": 12.8, "x": 0.88, "y": 0.88, "curve": [12.811, 0.872, 12.822, 0.856, 12.811, 0.872, 12.822, 0.856]}, {"time": 12.8333, "x": 0.856, "y": 0.856, "curve": [12.844, 0.856, 12.856, 0.929, 12.844, 0.856, 12.856, 0.929]}, {"time": 12.8667, "x": 0.929, "y": 0.929, "curve": [12.878, 0.929, 12.872, 0.899, 12.878, 0.929, 12.872, 0.899]}, {"time": 12.9, "x": 0.88, "y": 0.88, "curve": [12.911, 0.872, 12.922, 0.856, 12.911, 0.872, 12.922, 0.856]}, {"time": 12.9333, "x": 0.856, "y": 0.856, "curve": [12.944, 0.856, 12.956, 0.929, 12.944, 0.856, 12.956, 0.929]}, {"time": 12.9667, "x": 0.929, "y": 0.929, "curve": [12.978, 0.929, 12.989, 0.886, 12.978, 0.929, 12.989, 0.886]}, {"time": 13, "x": 0.865, "y": 0.865}]}, "blot_drops_control": {"translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9667, "x": -0.54, "y": -0.54, "curve": [13.189, -0.54, 13.411, 0, 13.112, -0.67, 13.411, -16.59]}, {"time": 13.6333, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9, "curve": [12.967, 0, 13.033, -67.84]}, {"time": 13.1, "value": -67.84}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": 41.44, "y": 7.91, "curve": [12.965, 77.65, 13.056, 273.93, 12.96, -38.99, 13.068, -211.23]}, {"time": 13.1333, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9, "curve": "stepped"}, {"time": 13.0667, "curve": [13.111, 1, 13.156, 0.4, 13.111, 1, 13.156, 0.4]}, {"time": 13.2, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "value": -35.08, "curve": [12.962, 32.76, 13.122, 77.81]}, {"time": 13.2333, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": -74.68, "y": 24.95, "curve": [12.986, -115.07, 13.122, -322.02, 12.997, 261.05, 13.141, -337.68]}, {"time": 13.2333, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9, "curve": "stepped"}, {"time": 13.1, "curve": [13.144, 1, 13.189, 0.4, 13.144, 1, 13.189, 0.4]}, {"time": 13.2333, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "value": 16.41, "curve": [12.997, 68.37, 13.1, 77.81]}, {"time": 13.2, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": -48.78, "y": -9.58, "curve": [12.954, -164.38, 13.1, -211.51, 12.978, -53.5, 13.129, -476.89]}, {"time": 13.2, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9, "curve": "stepped"}, {"time": 13.0667, "curve": [13.111, 1, 13.156, 0.4, 13.111, 1, 13.156, 0.4]}, {"time": 13.2, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": 54.1, "y": 60.11, "curve": [12.968, 209.62, 13.033, 276.96, 12.966, -14.18, 13.023, -99.2]}, {"time": 13.1, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": -53.64, "y": 83.69, "curve": [12.948, -164.57, 13.043, -315.66, 12.991, 159.13, 13.059, 84.28]}, {"time": 13.1333, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "value": 103.14, "curve": [12.989, 103.14, 13.152, 97.56]}, {"time": 13.1667, "value": 97.56}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": 35.48, "y": -53.58, "curve": [12.988, 58.13, 13.1, 37.99, 12.977, -113.89, 13.117, -373.65]}, {"time": 13.2, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 13.0667, "curve": [13.111, 1, 13.156, 0.4, 13.111, 1, 13.156, 0.4]}, {"time": 13.2, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": 10.55, "y": -19.73, "curve": [12.956, -6.01, 13.004, -21.01, 12.947, -79.55, 12.994, -201.42]}, {"time": 13.0667, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "value": -75.4, "curve": [12.942, -120.98, 13.027, -263.98]}, {"time": 13.1333, "value": -261.68}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9, "x": 9.31, "y": 91.31, "curve": [12.992, 118.46, 13.144, 297.6, 12.955, 320.53, 13.114, 364.92]}, {"time": 13.2667, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9, "curve": "stepped"}, {"time": 13.1333, "curve": [13.178, 1, 13.222, 0.4, 13.178, 1, 13.222, 0.4]}, {"time": 13.2667, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 12.6333, "curve": "stepped"}, {"time": 12.9333}], "translate": [{"curve": "stepped"}, {"time": 12.6333}, {"time": 12.9333, "x": -53.64, "y": 83.69, "curve": [13.022, -170.51, 13.173, -244.31, 13.094, -107.5, 13.209, -316.02]}, {"time": 13.3, "x": -277.78, "y": -598.88}]}}}, "t0_000000": {"slots": {"ARM_L_outline": {"rgba": [{"color": "0000007f"}]}, "ARM_R_outline": {"rgba": [{"color": "0000007f"}]}, "body_outline": {"rgba": [{"color": "0000007f"}]}, "mouth_outline": {"rgba": [{"color": "0000007f"}]}}}, "t0_405c80": {"slots": {"ARM_L_outline": {"rgba": [{"color": "405c80ff"}]}, "ARM_R_outline": {"rgba": [{"color": "405c80ff"}]}, "body_outline": {"rgba": [{"color": "405c80ff"}]}, "mouth_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}, {"time": 0.3}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}, {"time": 0.3}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}, {"time": 0.3}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}, {"time": 0.3}]}, "blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "body": {"attachment": [{"name": "body"}, {"time": 0.3}]}, "body_outline": {"attachment": [{"name": "body_outline"}, {"time": 0.3}]}, "eye": {"attachment": [{"name": "eye"}, {"time": 0.3}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}, {"time": 0.3}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}, {"time": 0.3}]}, "mouth": {"attachment": [{"name": "mouth"}, {"time": 0.3}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}, {"time": 0.3}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}, {"time": 0.3}]}, "pupil": {"attachment": [{"name": "pupil"}, {"time": 0.3}]}}, "bones": {"pupil": {"translate": [{"x": 4.69, "y": -23.09, "curve": [0.056, 4.69, 0.111, 2, 0.056, -23.09, 0.111, -4.78]}, {"time": 0.1667, "x": 2, "y": -4.78}], "scale": [{}]}, "Eyelid": {"rotate": [{}], "translate": [{"x": -23.56, "y": -3.85, "curve": [0.011, -23.56, 0.022, -36.95, 0.011, -3.85, 0.022, -7.84]}, {"time": 0.0333, "x": -36.95, "y": -7.84, "curve": [0.078, -36.95, 0.122, 33.2, 0.078, -7.84, 0.122, -11.22]}, {"time": 0.1667, "x": 33.2, "y": -11.22}]}, "mouth_base": {"rotate": [{}], "translate": [{"x": -3.11}], "scale": [{"y": 1.021}, {"time": 0.1667, "x": 1.284, "y": 1.311}]}, "eye": {"rotate": [{}], "translate": [{"y": -5.55}], "scale": [{}, {"time": 0.1667, "x": 1.358, "y": 1.358}]}, "Face": {"translate": [{"y": -5.55, "curve": "stepped"}, {"time": 0.0667, "y": -5.55, "curve": [0.111, 0, 0.156, 0, 0.111, -5.55, 0.156, 8.5]}, {"time": 0.2, "y": 8.5, "curve": "stepped"}, {"time": 0.2333, "y": 8.5}, {"time": 0.2667, "y": -7.08}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"x": 2.73, "y": 0.55, "curve": [0.011, 2.73, 0.022, 6.82, 0.011, 0.55, 0.022, 1.36]}, {"time": 0.0333, "x": 6.82, "y": 1.36}], "scale": [{"x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 15.11, "curve": [0.011, 15.11, 0.022, 29.24]}, {"time": 0.0333, "value": 29.24, "curve": [0.078, 29.24, 0.122, -25.37]}, {"time": 0.1667, "value": -25.37}]}, "body": {"rotate": [{"value": 5.76, "curve": [0.056, 5.76, 0.111, 21.27]}, {"time": 0.1667, "value": 21.27}]}, "Arm_R": {"rotate": [{"value": 33.08, "curve": [0.022, 32.57, 0.011, 32.31]}, {"time": 0.0333, "value": 30.77, "curve": [0.044, 30, 0.056, -97.36]}, {"time": 0.0667, "value": -104.9, "curve": [0.067, -105.15, 0.133, -62.26]}, {"time": 0.1667, "value": -62.26}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.044, 0, 0.056, -29.33, 0.044, 0, 0.056, -11.97]}, {"time": 0.0667, "x": -31.05, "y": -12.67, "curve": [0.075, -32.37, 0.133, 12.01, 0.068, -12.77, 0.133, -7.28]}, {"time": 0.1667, "x": 12.01, "y": -7.28}]}, "Arm_R2": {"rotate": [{"value": -64.62, "curve": [0.022, -64.62, 0.011, -154.98]}, {"time": 0.0333, "value": -154.98, "curve": [0.044, -154.98, 0.056, -13.83]}, {"time": 0.0667, "value": -13.83}]}, "Arm_L": {"rotate": [{"value": -3.98, "curve": [0.011, -3.98, 0.022, -53.15]}, {"time": 0.0333, "value": -53.15, "curve": [0.068, 26.93, 0.122, 27.45]}, {"time": 0.1667, "value": 27.45}]}, "Arm_L2": {"rotate": [{"value": 3.54, "curve": [0.011, 3.54, 0.022, -36.7]}, {"time": 0.0333, "value": -36.7, "curve": [0.068, 20.59, 0.122, 23.61]}, {"time": 0.1667, "value": 23.61}]}, "Mouth_control": {"rotate": [{"value": -44.48}, {"time": 0.1667, "value": -91.01}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -12.21, "y": 15.4, "curve": [0.422, -12.21, 0.544, -12.22, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.311, 1.137, 0.467, 1.2, 0.311, 1.137, 0.467, 1.2]}, {"time": 0.5667, "x": 1.2, "y": 1.2}]}, "cntr": {"translate": [{}], "scale": [{"x": 0.886, "y": 0.886, "curve": [0.011, 0.886, 0.022, 0.415, 0.011, 0.886, 0.022, 0.415]}, {"time": 0.0333, "x": 0.415, "y": 0.415, "curve": "stepped"}, {"time": 0.0667, "x": 0.415, "y": 0.415, "curve": [0.085, 0.701, 0.138, 0.899, 0.088, 0.708, 0.138, 0.899]}, {"time": 0.1667, "x": 0.88, "y": 0.88, "curve": [0.178, 0.872, 0.189, 0.856, 0.178, 0.872, 0.189, 0.856]}, {"time": 0.2, "x": 0.856, "y": 0.856, "curve": [0.211, 0.856, 0.222, 0.929, 0.211, 0.856, 0.222, 0.929]}, {"time": 0.2333, "x": 0.929, "y": 0.929, "curve": [0.244, 0.929, 0.238, 0.899, 0.244, 0.929, 0.238, 0.899]}, {"time": 0.2667, "x": 0.88, "y": 0.88, "curve": [0.278, 0.872, 0.289, 0.856, 0.278, 0.872, 0.289, 0.856]}, {"time": 0.3, "x": 0.856, "y": 0.856, "curve": [0.311, 0.856, 0.322, 0.929, 0.311, 0.856, 0.322, 0.929]}, {"time": 0.3333, "x": 0.929, "y": 0.929, "curve": [0.344, 0.929, 0.356, 0.886, 0.344, 0.929, 0.356, 0.886]}, {"time": 0.3667, "x": 0.865, "y": 0.865}]}, "blot_drops_control": {"translate": [{}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}}}, "t1_Death2": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}, {"time": 0.3667}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}, {"time": 0.3667}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}, {"time": 0.3667}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}, {"time": 0.3667}]}, "blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6667}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5667}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop2"}, {"time": 0.7}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3667, "name": "blot_drop1"}, {"time": 0.7333}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5333}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3333, "name": "blot_drop1"}, {"time": 0.5}]}, "body": {"attachment": [{"name": "body"}, {"time": 0.3667}]}, "body_outline": {"attachment": [{"name": "body_outline"}, {"time": 0.3667}]}, "eye": {"attachment": [{"name": "eye"}, {"time": 0.3333}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}, {"time": 0.3667}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}, {"time": 0.3667}]}, "mouth": {"attachment": [{"name": "mouth"}, {"time": 0.3667}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}, {"time": 0.3667}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}, {"time": 0.3667}]}, "pupil": {"attachment": [{"name": "pupil"}, {"time": 0.3333}]}}, "bones": {"pupil": {"rotate": [{"curve": [0.056, 0, 0.111, 14.3]}, {"time": 0.1667, "value": 14.3}], "translate": [{"x": 4.69, "y": -23.09, "curve": [0.056, 4.69, 0.111, 0.53, 0.056, -23.09, 0.111, -5.16]}, {"time": 0.1667, "x": 0.53, "y": -5.16}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": [0.144, 1, 0.189, 0.39, 0.144, 1, 0.189, 0.39]}, {"time": 0.2333, "x": 0.39, "y": 0.39}]}, "Eyelid": {"rotate": [{}], "translate": [{"x": -23.56, "y": -3.85, "curve": [0.011, -23.56, 0.022, -36.95, 0.011, -3.85, 0.022, -7.84]}, {"time": 0.0333, "x": -36.95, "y": -7.84, "curve": [0.078, -36.95, 0.122, 31.64, 0.078, -7.84, 0.122, 14.16]}, {"time": 0.1667, "x": 31.64, "y": 14.16}]}, "mouth_base": {"rotate": [{}], "translate": [{"x": -3.11}], "scale": [{"y": 1.021}, {"time": 0.1667, "x": 1.284, "y": 1.311}]}, "eye": {"rotate": [{"curve": "stepped"}, {"time": 0.0667}, {"time": 0.1667, "value": -16.65}], "translate": [{"y": -5.55}], "scale": [{"curve": [0.056, 1, 0.111, 1.358, 0.056, 1, 0.111, 1.358]}, {"time": 0.1667, "x": 1.358, "y": 1.358}]}, "Face": {"translate": [{"y": -5.55, "curve": "stepped"}, {"time": 0.0667, "y": -5.55, "curve": [0.111, 0, 0.156, 0, 0.111, -5.55, 0.156, 32.33]}, {"time": 0.2, "y": 32.33, "curve": "stepped"}, {"time": 0.3, "y": 32.33}, {"time": 0.3333, "y": -1.22}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.144, 1, 0.156, 0.496, 0.144, 1, 0.156, 1.223]}, {"time": 0.2333, "x": 0.496, "y": 1.223}]}, "low_cntr": {"translate": [{"x": 2.73, "y": 0.55, "curve": [0.011, 2.73, 0.022, 6.82, 0.011, 0.55, 0.022, 1.36]}, {"time": 0.0333, "x": 6.82, "y": 1.36}], "scale": [{"x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 15.11, "curve": [0.011, 15.11, 0.022, 29.24]}, {"time": 0.0333, "value": 29.24, "curve": [0.078, 29.24, 0.122, -25.37]}, {"time": 0.1667, "value": -25.37}]}, "body": {"rotate": [{"value": 5.76, "curve": [0.056, 5.76, 0.111, 21.27]}, {"time": 0.1667, "value": 21.27}]}, "Arm_R": {"rotate": [{"value": 33.08, "curve": [0.022, 32.57, 0.011, 32.31]}, {"time": 0.0333, "value": 30.77, "curve": [0.044, 30, 0.056, -97.36]}, {"time": 0.0667, "value": -104.9, "curve": [0.067, -105.15, 0.133, -62.26]}, {"time": 0.1667, "value": -62.26, "curve": "stepped"}, {"time": 0.3, "value": -62.26, "curve": "stepped"}, {"time": 0.3333, "value": -62.26}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.044, 0, 0.056, -29.33, 0.044, 0, 0.056, -11.97]}, {"time": 0.0667, "x": -31.05, "y": -12.67, "curve": [0.075, -32.37, 0.133, 13.47, 0.068, -12.77, 0.133, 12.01]}, {"time": 0.1667, "x": 13.47, "y": 12.01, "curve": "stepped"}, {"time": 0.3, "x": 13.47, "y": 12.01}, {"time": 0.3333, "x": 12.01, "y": -7.28}]}, "Arm_R2": {"rotate": [{"value": -64.62, "curve": [0.022, -64.62, 0.011, -154.98]}, {"time": 0.0333, "value": -154.98, "curve": [0.044, -154.98, 0.056, -13.83]}, {"time": 0.0667, "value": -13.83}]}, "Arm_L": {"rotate": [{"value": -3.98, "curve": [0.011, -3.98, 0.022, -53.15]}, {"time": 0.0333, "value": -53.15, "curve": [0.068, 26.93, 0.122, 27.45]}, {"time": 0.1667, "value": 27.45}], "translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.078, 0, 0.122, -7.78, 0.078, 0, 0.122, -10.59]}, {"time": 0.1667, "x": -7.78, "y": -10.59, "curve": "stepped"}, {"time": 0.3, "x": -7.78, "y": -10.59}, {"time": 0.3333, "x": -7.78, "y": -2.02}]}, "Arm_L2": {"rotate": [{"value": 3.54, "curve": [0.011, 3.54, 0.022, -36.7]}, {"time": 0.0333, "value": -36.7, "curve": [0.068, 20.59, 0.122, 23.61]}, {"time": 0.1667, "value": 23.61}]}, "Mouth_control": {"rotate": [{"value": -44.48}, {"time": 0.1667, "value": -91.01}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3667, "x": -12.21, "y": 15.4, "curve": [0.489, -12.21, 0.611, -12.22, 0.442, 7.46, 0.611, 1.44]}, {"time": 0.7333, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.3333, "x": 0.449, "y": 0.449, "curve": [0.378, 1.137, 0.533, 1.2, 0.378, 1.137, 0.533, 1.2]}, {"time": 0.6333, "x": 1.2, "y": 1.2}]}, "cntr": {"translate": [{}], "scale": [{"x": 0.886, "y": 0.886, "curve": [0.011, 0.886, 0.022, 0.415, 0.011, 0.886, 0.022, 0.415]}, {"time": 0.0333, "x": 0.415, "y": 0.415, "curve": "stepped"}, {"time": 0.0667, "x": 0.415, "y": 0.415, "curve": [0.085, 0.701, 0.138, 0.899, 0.088, 0.708, 0.138, 0.899]}, {"time": 0.1667, "x": 0.88, "y": 0.88, "curve": [0.178, 0.872, 0.189, 0.856, 0.178, 0.872, 0.189, 0.856]}, {"time": 0.2, "x": 0.856, "y": 0.856, "curve": [0.211, 0.856, 0.222, 0.929, 0.211, 0.856, 0.222, 0.929]}, {"time": 0.2333, "x": 0.929, "y": 0.929, "curve": [0.244, 0.929, 0.238, 0.899, 0.244, 0.929, 0.238, 0.899]}, {"time": 0.2667, "x": 0.88, "y": 0.88, "curve": [0.278, 0.872, 0.289, 0.856, 0.278, 0.872, 0.289, 0.856]}, {"time": 0.3, "x": 0.856, "y": 0.856, "curve": [0.311, 0.856, 0.322, 0.929, 0.311, 0.856, 0.322, 0.929]}, {"time": 0.3333, "x": 0.929, "y": 0.929, "curve": [0.344, 0.929, 0.356, 0.886, 0.344, 0.929, 0.356, 0.886]}, {"time": 0.3667, "x": 0.865, "y": 0.865}]}, "blot_drops_control": {"translate": [{}, {"time": 0.4, "x": -0.54, "y": -0.54, "curve": [0.622, -0.54, 0.844, 0, 0.545, -0.67, 0.844, -16.59]}, {"time": 1.0667, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.4, 0, 0.467, -67.84]}, {"time": 0.5333, "value": -67.84}], "translate": [{}, {"time": 0.3333, "x": 41.44, "y": 7.91, "curve": [0.398, 77.65, 0.489, 273.93, 0.394, -38.99, 0.502, -211.23]}, {"time": 0.5667, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.3333, "value": -35.08, "curve": [0.396, 32.76, 0.556, 77.81]}, {"time": 0.6667, "value": 77.81}], "translate": [{}, {"time": 0.3333, "x": -74.68, "y": 24.95, "curve": [0.419, -115.07, 0.556, -322.02, 0.43, 261.05, 0.574, -337.68]}, {"time": 0.6667, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 1, 0.622, 0.4, 0.578, 1, 0.622, 0.4]}, {"time": 0.6667, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.3333, "value": 16.41, "curve": [0.43, 68.37, 0.533, 77.81]}, {"time": 0.6333, "value": 77.81}], "translate": [{}, {"time": 0.3333, "x": -48.78, "y": -9.58, "curve": [0.387, -164.38, 0.533, -211.51, 0.411, -53.5, 0.562, -476.89]}, {"time": 0.6333, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{}, {"time": 0.3333, "x": 54.1, "y": 60.11, "curve": [0.401, 209.62, 0.467, 276.96, 0.4, -14.18, 0.456, -99.2]}, {"time": 0.5333, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{}, {"time": 0.3333, "x": -53.64, "y": 83.69, "curve": [0.381, -164.57, 0.476, -315.66, 0.424, 159.13, 0.493, 84.28]}, {"time": 0.5667, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.3333, "value": 103.14, "curve": [0.422, 103.14, 0.585, 97.56]}, {"time": 0.6, "value": 97.56}], "translate": [{}, {"time": 0.3333, "x": 35.48, "y": -53.58, "curve": [0.422, 58.13, 0.533, 37.99, 0.411, -113.89, 0.55, -373.65]}, {"time": 0.6333, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.3333}], "translate": [{}, {"time": 0.3333, "x": 10.55, "y": -19.73, "curve": [0.39, -6.01, 0.437, -21.01, 0.38, -79.55, 0.427, -201.42]}, {"time": 0.5, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.3333, "value": -75.4, "curve": [0.376, -120.98, 0.46, -263.98]}, {"time": 0.5667, "value": -261.68}], "translate": [{}, {"time": 0.3333, "x": 9.31, "y": 91.31, "curve": [0.425, 118.46, 0.578, 297.6, 0.389, 320.53, 0.547, 364.92]}, {"time": 0.7, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": [0.611, 1, 0.656, 0.4, 0.611, 1, 0.656, 0.4]}, {"time": 0.7, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3667}], "translate": [{}, {"time": 0.3667, "x": -53.64, "y": 83.69, "curve": [0.455, -170.51, 0.607, -244.31, 0.527, -107.5, 0.642, -316.02]}, {"time": 0.7333, "x": -277.78, "y": -598.88}]}}, "attachments": {"default": {"body": {"body": {"deform": [{}, {"time": 0.1667, "vertices": [5.45039, -0.61831, 2.27799, -0.46885, 0.72376, -0.88275, 0.00934, -1.72518, -0.13393, -1.72006, -0.32061, -4.52009, -0.6948, -4.4779, -0.57001, -7.87337, -1.22167, -7.7989, -0.79009, -10.82472, -1.68597, -10.72181, -0.98096, -13.38565, -2.08874, -13.25806, -1.14968, -15.65578, -2.44529, -15.50634, -1.30389, -17.74344, -2.77224, -17.574, -1.36023, -17.76988, -2.8306, -17.59568, -1.32457, -16.82767, -2.71685, -16.65969, -1.14691, -14.36858, -2.33569, -14.22383, -0.95039, -11.72025, -1.91999, -11.60095, -0.73622, -8.8399, -1.46747, -8.74831, -0.5815, -6.02529, -1.07973, -5.95625, -0.98346, -3.57805, -1.38572, -1.60656, -1.8092, -0.91397, -2.93843, -0.65353, -1.71327, 2.98645, -1.08733, 6.2927, -0.56132, 6.36121, -0.47646, 10.78264, 0.42006, 10.78493, 0.03711, 15.16606, 1.29568, 15.1106, 0.38497, 25.55962, 2.50496, 25.43941, 0.30495, 30.42551, 2.82907, 30.29515, 0.11276, 35.56732, 3.06433, 35.43515, -0.33857, 39.2031, 2.91637, 39.09586, 0.21418, 39.06704, 3.45592, 38.91437, 0.55956, 37.31165, 3.65434, 37.13641, 0.88794, 34.97176, 3.78743, 34.77726, 0.77881, 32.01904, 3.43353, 31.84383, 0.47182, 29.09737, 2.88507, 28.95773, 0.1334, 26.01688, 2.29219, 25.91597, -0.22312, 21.76325, 1.58392, 21.70657, -0.41585, 17.66802, 1.05191, 17.6415, -0.78888, 13.19241, 0.3087, 13.2123, -1.48901, 8.45501, -0.78211, 8.54937, -0.11101, 4.22962, 0.24039, 4.22417, 1.70496, 2.18164, 1.88016, 2.03253, 5.53261, 1.19283, 5.61254, 0.72942, 8.675, 0.04489, 13.8912, -0.95569, 19.09223, -1.77216, 23.21892, -2.1593, 22.12336, -2.21667, 17.25381, -1.74922, 12.59062, -1.30314, -0.76133, 12.71819, 0.29673, 12.73745, -0.77568, -9.6489, -1.57398, -9.55124, -0.80407, -10.50715, -1.67354, -10.40415, 5.33533, -2.05274, 5.14654, -2.48857, 8.09597, 3.82238, 8.38523, 3.13709, 0.80047, 21.44814, 2.57779, 21.30759], "curve": "stepped"}, {"time": 0.3, "vertices": [5.45039, -0.61831, 2.27799, -0.46885, 0.72376, -0.88275, 0.00934, -1.72518, -0.13393, -1.72006, -0.32061, -4.52009, -0.6948, -4.4779, -0.57001, -7.87337, -1.22167, -7.7989, -0.79009, -10.82472, -1.68597, -10.72181, -0.98096, -13.38565, -2.08874, -13.25806, -1.14968, -15.65578, -2.44529, -15.50634, -1.30389, -17.74344, -2.77224, -17.574, -1.36023, -17.76988, -2.8306, -17.59568, -1.32457, -16.82767, -2.71685, -16.65969, -1.14691, -14.36858, -2.33569, -14.22383, -0.95039, -11.72025, -1.91999, -11.60095, -0.73622, -8.8399, -1.46747, -8.74831, -0.5815, -6.02529, -1.07973, -5.95625, -0.98346, -3.57805, -1.38572, -1.60656, -1.8092, -0.91397, -2.93843, -0.65353, -1.71327, 2.98645, -1.08733, 6.2927, -0.56132, 6.36121, -0.47646, 10.78264, 0.42006, 10.78493, 0.03711, 15.16606, 1.29568, 15.1106, 0.38497, 25.55962, 2.50496, 25.43941, 0.30495, 30.42551, 2.82907, 30.29515, 0.11276, 35.56732, 3.06433, 35.43515, -0.33857, 39.2031, 2.91637, 39.09586, 0.21418, 39.06704, 3.45592, 38.91437, 0.55956, 37.31165, 3.65434, 37.13641, 0.88794, 34.97176, 3.78743, 34.77726, 0.77881, 32.01904, 3.43353, 31.84383, 0.47182, 29.09737, 2.88507, 28.95773, 0.1334, 26.01688, 2.29219, 25.91597, -0.22312, 21.76325, 1.58392, 21.70657, -0.41585, 17.66802, 1.05191, 17.6415, -0.78888, 13.19241, 0.3087, 13.2123, -1.48901, 8.45501, -0.78211, 8.54937, -0.11101, 4.22962, 0.24039, 4.22417, 1.70496, 2.18164, 1.88016, 2.03253, 5.53261, 1.19283, 5.61254, 0.72942, 8.675, 0.04489, 13.8912, -0.95569, 19.09223, -1.77216, 23.21892, -2.1593, 22.12336, -2.21667, 17.25381, -1.74922, 12.59062, -1.30314, -0.76133, 12.71819, 0.29673, 12.73745, -0.77568, -9.6489, -1.57398, -9.55124, -0.80407, -10.50715, -1.67354, -10.40415, 5.33533, -2.05274, 5.14654, -2.48857, 8.09597, 3.82238, 8.38523, 3.13709, 0.80047, 21.44814, 2.57779, 21.30759]}, {"time": 0.3333}]}}, "body_outline": {"body_outline": {"deform": [{}, {"time": 0.1667, "vertices": [4.13685, -0.49406, 1.12572, -0.28342, 0.06498, -0.86647, -0.00719, -0.86889, -0.14982, -2.24371, -0.33561, -2.22355, -0.39245, -5.51033, -0.84859, -5.45876, -0.6304, -8.69179, -1.34977, -8.6095, -0.80219, -10.98156, -1.71099, -10.8771, -1.02435, -13.92952, -2.17707, -13.79646, -1.20031, -16.2088, -2.54166, -16.05326, -1.36067, -17.8059, -2.834, -17.63155, -1.43366, -18.12569, -2.93327, -17.94416, -1.24122, -15.58564, -2.5307, -15.42886, -1.0444, -12.96334, -2.11687, -12.83193, -0.86128, -10.51546, -1.7312, -10.4077, -0.57592, -6.68733, -1.12913, -6.61648, -0.74681, -3.34908, -1.02228, -3.27557, -1.24562, -1.17444, -1.98253, -1.00154, -2.78082, -1.34209, -1.97916, 0.87959, -1.15329, 4.36389, -0.7872, 4.44453, -0.35791, 9.54297, 0.43525, 9.5397, 0.31828, 15.61771, 1.61336, 15.53736, 0.36506, 23.65907, 2.32738, 23.54707, 0.22097, 30.27566, 2.73297, 30.15276, 0.19501, 37.12032, 3.27522, 36.97598, 0.2232, 39.4604, 3.49756, 39.30561, 0.32039, 37.62643, 3.44209, 37.46994, 1.11952, 32.60995, 3.82204, 32.40443, 0.3697, 27.23782, 2.62902, 27.11305, -0.83704, 20.76782, 0.88947, 20.76558, -1.28731, 14.38303, -0.08921, 14.44016, -2.00664, 8.14803, -1.32353, 8.2864, -1.21613, 3.45237, -0.92545, 3.54134, 1.68646, 1.39556, 1.79647, 1.2507, 6.70024, 0.62377, 6.72894, 0.06541, 12.06307, -0.96901, 18.6663, -1.72482, 22.918, -2.17729, 18.77811, -1.88711, 12.56398, -1.29942, 4.49743, -2.45831, -0.29805, 1.09624, 7.20528, 4.48819, 7.55281, 3.87456, -0.79523, 12.39807, 0.2364, 12.42124], "curve": "stepped"}, {"time": 0.3, "vertices": [4.13685, -0.49406, 1.12572, -0.28342, 0.06498, -0.86647, -0.00719, -0.86889, -0.14982, -2.24371, -0.33561, -2.22355, -0.39245, -5.51033, -0.84859, -5.45876, -0.6304, -8.69179, -1.34977, -8.6095, -0.80219, -10.98156, -1.71099, -10.8771, -1.02435, -13.92952, -2.17707, -13.79646, -1.20031, -16.2088, -2.54166, -16.05326, -1.36067, -17.8059, -2.834, -17.63155, -1.43366, -18.12569, -2.93327, -17.94416, -1.24122, -15.58564, -2.5307, -15.42886, -1.0444, -12.96334, -2.11687, -12.83193, -0.86128, -10.51546, -1.7312, -10.4077, -0.57592, -6.68733, -1.12913, -6.61648, -0.74681, -3.34908, -1.02228, -3.27557, -1.24562, -1.17444, -1.98253, -1.00154, -2.78082, -1.34209, -1.97916, 0.87959, -1.15329, 4.36389, -0.7872, 4.44453, -0.35791, 9.54297, 0.43525, 9.5397, 0.31828, 15.61771, 1.61336, 15.53736, 0.36506, 23.65907, 2.32738, 23.54707, 0.22097, 30.27566, 2.73297, 30.15276, 0.19501, 37.12032, 3.27522, 36.97598, 0.2232, 39.4604, 3.49756, 39.30561, 0.32039, 37.62643, 3.44209, 37.46994, 1.11952, 32.60995, 3.82204, 32.40443, 0.3697, 27.23782, 2.62902, 27.11305, -0.83704, 20.76782, 0.88947, 20.76558, -1.28731, 14.38303, -0.08921, 14.44016, -2.00664, 8.14803, -1.32353, 8.2864, -1.21613, 3.45237, -0.92545, 3.54134, 1.68646, 1.39556, 1.79647, 1.2507, 6.70024, 0.62377, 6.72894, 0.06541, 12.06307, -0.96901, 18.6663, -1.72482, 22.918, -2.17729, 18.77811, -1.88711, 12.56398, -1.29942, 4.49743, -2.45831, -0.29805, 1.09624, 7.20528, 4.48819, 7.55281, 3.87456, -0.79523, 12.39807, 0.2364, 12.42124]}, {"time": 0.3333}]}}}}}, "t1_IDLE": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}]}, "blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"pupil": {"translate": [{"x": 12.53, "y": 2.3, "curve": "stepped"}, {"time": 0.3, "x": 12.53, "y": 2.3, "curve": [0.754, 15.3, 2.573, 16.67, 0.755, 5.16, 2.573, 5.11]}, {"time": 3.0333, "x": 15.01, "y": 3.56, "curve": [3.153, 14.58, 3.257, -10.5, 3.156, 3.15, 3.278, 3.7]}, {"time": 3.4, "x": -11.48, "y": 3.7, "curve": [3.604, -12.89, 5.36, -13.33, 3.608, 3.7, 5.359, 3.8]}, {"time": 5.5667, "x": -12.34, "y": 3.8, "curve": [5.711, -11.66, 5.879, 11.79, 5.711, 3.8, 5.882, 1.56]}, {"time": 6, "x": 12.53, "y": 2.3}], "scale": [{}]}, "Eyelid": {"rotate": [{}], "translate": [{}]}, "mouth_base": {"rotate": [{}], "translate": [{"x": 1.63, "y": 0.44, "curve": [0.034, 1, 0.067, 0.36, 0.034, 0.2, 0.067, 0]}, {"time": 0.1, "curve": [0.383, -3.11, 2.597, -3.11, 0.403, 0, 2.597, 0]}, {"time": 2.9, "x": -3.11, "curve": [3, -3.11, 3.1, 2.58, 3, 0, 3.1, 1.38]}, {"time": 3.2, "x": 2.58, "y": 1.38, "curve": "stepped"}, {"time": 3.2667, "x": 2.58, "y": 1.38, "curve": [3.3, 2.58, 3.533, 0, 3.3, 1.38, 3.533, 0]}, {"time": 3.6667, "curve": [4.218, 0, 4.217, 2.58, 4.218, 0, 4.217, 1.38]}, {"time": 4.3, "x": 2.58, "y": 1.38}, {"time": 5.4333, "curve": "stepped"}, {"time": 5.5, "curve": [5.589, 0, 5.678, 3.78, 5.589, 0, 5.678, 1.38]}, {"time": 5.7667, "x": 3.78, "y": 1.38, "curve": "stepped"}, {"time": 5.8333, "x": 3.78, "y": 1.38, "curve": [5.889, 3.78, 5.945, 2.69, 5.889, 1.38, 5.945, 0.85]}, {"time": 6, "x": 1.63, "y": 0.44}], "scale": [{"y": 0.973, "curve": [0.034, 1, 0.067, 1, 0.034, 0.988, 0.067, 1]}, {"time": 0.1, "curve": [0.403, 1, 2.597, 1, 0.403, 1, 2.597, 1.021]}, {"time": 2.9, "y": 1.021, "curve": [3, 1, 3.1, 1, 3, 1.021, 3.1, 0.951]}, {"time": 3.2, "y": 0.951, "curve": "stepped"}, {"time": 3.2667, "y": 0.951, "curve": [3.4, 1, 3.533, 1, 3.4, 0.951, 3.533, 1]}, {"time": 3.6667, "curve": [4.218, 1, 4.217, 1, 4.218, 1, 4.217, 0.951]}, {"time": 4.3, "y": 0.951, "curve": [4.329, 1, 4.753, 1, 4.329, 0.951, 4.753, 1]}, {"time": 5.4333, "curve": "stepped"}, {"time": 5.5, "curve": [5.589, 1, 5.678, 1, 5.589, 1, 5.678, 0.916]}, {"time": 5.7667, "y": 0.916, "curve": "stepped"}, {"time": 5.8333, "y": 0.916, "curve": [5.889, 1, 5.945, 1, 5.889, 0.916, 5.945, 0.949]}, {"time": 6, "y": 0.973}]}, "eye": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Face": {"translate": [{"y": -5.55, "curve": "stepped"}, {"time": 3.0333, "y": -5.55, "curve": [3.156, 0, 3.278, 0, 3.156, -5.55, 3.278, 5.12]}, {"time": 3.4, "y": 5.12, "curve": "stepped"}, {"time": 5.5667, "y": 5.12, "curve": [5.711, 0, 5.878, 0, 5.711, 5.12, 5.878, -5.55]}, {"time": 6, "y": -5.55}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 3.0333, "x": 1.22, "y": -5.15, "curve": [3.156, 1.22, 3.278, 0.98, 3.156, -5.15, 3.278, 3.01]}, {"time": 3.4, "x": 0.98, "y": 3.01, "curve": "stepped"}, {"time": 5.6, "x": 0.98, "y": 3.01, "curve": [5.722, 0.98, 5.844, 1.22, 5.722, 3.01, 5.844, -5.15]}, {"time": 5.9667, "x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, 25.35]}, {"time": 1, "y": 25.35, "curve": [1.333, 0, 1.667, 0, 1.333, 25.35, 1.667, 0]}, {"time": 2, "curve": [2.333, 0, 2.667, 0, 2.333, 0, 2.667, 25.35]}, {"time": 3, "y": 25.35, "curve": [3.333, 0, 3.667, 0, 3.333, 25.35, 3.667, 0]}, {"time": 4, "curve": [4.333, 0, 4.667, 0, 4.333, 0, 4.667, 25.35]}, {"time": 5, "y": 25.35, "curve": [5.333, 0, 5.667, 0, 5.333, 25.35, 5.667, 0]}, {"time": 6}], "scale": [{"x": 1.009, "y": 0.991, "curve": [0.136, 1.02, 0.268, 1.03, 0.136, 0.98, 0.268, 0.97]}, {"time": 0.4, "x": 1.03, "y": 0.97, "curve": [0.733, 1.03, 1.067, 0.97, 0.733, 0.97, 1.067, 1.03]}, {"time": 1.4, "x": 0.97, "y": 1.03, "curve": [1.601, 0.97, 1.8, 0.992, 1.601, 1.03, 1.8, 1.008]}, {"time": 2, "x": 1.009, "y": 0.991, "curve": [2.136, 1.02, 2.268, 1.03, 2.136, 0.98, 2.268, 0.97]}, {"time": 2.4, "x": 1.03, "y": 0.97, "curve": [2.733, 1.03, 3.067, 0.97, 2.733, 0.97, 3.067, 1.03]}, {"time": 3.4, "x": 0.97, "y": 1.03, "curve": [3.601, 0.97, 3.8, 0.992, 3.601, 1.03, 3.8, 1.008]}, {"time": 4, "x": 1.009, "y": 0.991, "curve": [4.136, 1.02, 4.268, 1.03, 4.136, 0.98, 4.268, 0.97]}, {"time": 4.4, "x": 1.03, "y": 0.97, "curve": [4.733, 1.03, 5.067, 0.97, 4.733, 0.97, 5.067, 1.03]}, {"time": 5.4, "x": 0.97, "y": 1.03, "curve": [5.601, 0.97, 5.802, 0.992, 5.601, 1.03, 5.802, 1.008]}, {"time": 6, "x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 1.32, "curve": [0.224, 2.81, 0.446, 6.72]}, {"time": 0.6667, "value": 6.72, "curve": [1, 6.72, 1.333, 0]}, {"time": 1.6667, "curve": [1.779, 0, 1.889, 0.58]}, {"time": 2, "value": 1.32, "curve": [2.224, 2.81, 2.446, 6.72]}, {"time": 2.6667, "value": 6.72, "curve": [3, 6.72, 3.333, 0]}, {"time": 3.6667, "curve": [3.779, 0, 3.889, 0.58]}, {"time": 4, "value": 1.32, "curve": [4.224, 2.81, 4.446, 6.72]}, {"time": 4.6667, "value": 6.72, "curve": [5, 6.72, 5.333, 0]}, {"time": 5.6667, "curve": [5.779, 0, 5.891, 0.56]}, {"time": 6, "value": 1.32}]}, "body": {"rotate": [{}]}, "Arm_R": {"rotate": [{"value": -1.38, "curve": [0.322, -1.38, 0.644, 6.43]}, {"time": 0.9667, "value": 6.43, "curve": [1.311, 6.43, 1.656, -1.38]}, {"time": 2, "value": -1.38, "curve": [2.322, -1.38, 2.644, 6.43]}, {"time": 2.9667, "value": 6.43, "curve": [3.311, 6.43, 3.656, -1.38]}, {"time": 4, "value": -1.38, "curve": [4.322, -1.38, 4.644, 6.43]}, {"time": 4.9667, "value": 6.43, "curve": [5.311, 6.43, 5.656, -1.38]}, {"time": 6, "value": -1.38}], "translate": [{}]}, "Arm_R2": {"rotate": [{"value": -7.3, "curve": [0.079, -8.33, 0.156, -9.06]}, {"time": 0.2333, "value": -9.06, "curve": [0.556, -9.06, 0.878, 4.16]}, {"time": 1.2, "value": 4.16, "curve": [1.468, 4.16, 1.733, -3.87]}, {"time": 2, "value": -7.3, "curve": [2.079, -8.33, 2.156, -9.06]}, {"time": 2.2333, "value": -9.06, "curve": [2.556, -9.06, 2.878, 4.16]}, {"time": 3.2, "value": 4.16, "curve": [3.468, 4.16, 3.733, -3.87]}, {"time": 4, "value": -7.3, "curve": [4.079, -8.33, 4.156, -9.06]}, {"time": 4.2333, "value": -9.06, "curve": [4.556, -9.06, 4.878, 4.16]}, {"time": 5.2, "value": 4.16, "curve": [5.468, 4.16, 5.735, -3.74]}, {"time": 6, "value": -7.3}]}, "Arm_L": {"rotate": [{"value": 4.12, "curve": [0.322, 4.12, 0.644, -2.03]}, {"time": 0.9667, "value": -2.03, "curve": [1.311, -2.03, 1.656, 4.12]}, {"time": 2, "value": 4.12, "curve": [2.322, 4.12, 2.644, -2.03]}, {"time": 2.9667, "value": -2.03, "curve": [3.311, -2.03, 3.656, 4.12]}, {"time": 4, "value": 4.12, "curve": [4.322, 4.12, 4.644, -2.03]}, {"time": 4.9667, "value": -2.03, "curve": [5.311, -2.03, 5.656, 4.12]}, {"time": 6, "value": 4.12}]}, "Arm_L2": {"rotate": [{"value": 15.74, "curve": [0.113, 17.36, 0.223, 18.62]}, {"time": 0.3333, "value": 18.62, "curve": [0.656, 18.62, 0.978, 6.94]}, {"time": 1.3, "value": 6.94, "curve": [1.534, 6.94, 1.767, 12.37]}, {"time": 2, "value": 15.74, "curve": [2.113, 17.36, 2.223, 18.62]}, {"time": 2.3333, "value": 18.62, "curve": [2.656, 18.62, 2.978, 6.94]}, {"time": 3.3, "value": 6.94, "curve": [3.534, 6.94, 3.767, 12.37]}, {"time": 4, "value": 15.74, "curve": [4.113, 17.36, 4.223, 18.62]}, {"time": 4.3333, "value": 18.62, "curve": [4.656, 18.62, 4.978, 6.94]}, {"time": 5.3, "value": 6.94, "curve": [5.534, 6.94, 5.768, 12.28]}, {"time": 6, "value": 15.74}]}, "Mouth_control": {"rotate": [{}]}, "blot": {"translate": [{}], "scale": [{}]}, "cntr": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_IDLE2": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}]}, "blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"pupil": {"translate": [{"x": 4.69, "y": -23.09}], "scale": [{}]}, "Eyelid": {"rotate": [{}], "translate": [{"x": -14.63, "y": -1.18}]}, "mouth_base": {"rotate": [{}], "translate": [{"x": -3.11}], "scale": [{"y": 1.021}]}, "eye": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "Face": {"translate": [{"y": -5.55}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, 18.93]}, {"time": 1, "y": 18.93, "curve": [1.333, 0, 1.667, 0, 1.333, 18.93, 1.667, 0]}, {"time": 2, "curve": [2.333, 0, 2.667, 0, 2.333, 0, 2.667, 18.93]}, {"time": 3, "y": 18.93, "curve": [3.333, 0, 3.667, 0, 3.333, 18.93, 3.667, 0]}, {"time": 4, "curve": [4.333, 0, 4.667, 0, 4.333, 0, 4.667, 18.93]}, {"time": 5, "y": 18.93, "curve": [5.333, 0, 5.667, 0, 5.333, 18.93, 5.667, 0]}, {"time": 6}], "scale": [{"x": 1.009, "y": 0.991, "curve": [0.136, 1.02, 0.268, 1.03, 0.136, 0.98, 0.268, 0.97]}, {"time": 0.4, "x": 1.03, "y": 0.97, "curve": [0.733, 1.03, 1.067, 0.97, 0.733, 0.97, 1.067, 1.03]}, {"time": 1.4, "x": 0.97, "y": 1.03, "curve": [1.601, 0.97, 1.8, 0.992, 1.601, 1.03, 1.8, 1.008]}, {"time": 2, "x": 1.009, "y": 0.991, "curve": [2.136, 1.02, 2.268, 1.03, 2.136, 0.98, 2.268, 0.97]}, {"time": 2.4, "x": 1.03, "y": 0.97, "curve": [2.733, 1.03, 3.067, 0.97, 2.733, 0.97, 3.067, 1.03]}, {"time": 3.4, "x": 0.97, "y": 1.03, "curve": [3.601, 0.97, 3.8, 0.992, 3.601, 1.03, 3.8, 1.008]}, {"time": 4, "x": 1.009, "y": 0.991, "curve": [4.136, 1.02, 4.268, 1.03, 4.136, 0.98, 4.268, 0.97]}, {"time": 4.4, "x": 1.03, "y": 0.97, "curve": [4.733, 1.03, 5.067, 0.97, 4.733, 0.97, 5.067, 1.03]}, {"time": 5.4, "x": 0.97, "y": 1.03, "curve": [5.601, 0.97, 5.802, 0.992, 5.601, 1.03, 5.802, 1.008]}, {"time": 6, "x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 5.68, "curve": [0.224, 7.17, 0.446, 11.08]}, {"time": 0.6667, "value": 11.08, "curve": [1, 11.08, 1.333, 4.36]}, {"time": 1.6667, "value": 4.36, "curve": [1.779, 4.36, 1.889, 4.94]}, {"time": 2, "value": 5.68, "curve": [2.224, 7.17, 2.446, 11.08]}, {"time": 2.6667, "value": 11.08, "curve": [3, 11.08, 3.333, 4.36]}, {"time": 3.6667, "value": 4.36, "curve": [3.779, 4.36, 3.889, 4.94]}, {"time": 4, "value": 5.68, "curve": [4.224, 7.17, 4.446, 11.08]}, {"time": 4.6667, "value": 11.08, "curve": [5, 11.08, 5.333, 4.36]}, {"time": 5.6667, "value": 4.36, "curve": [5.779, 4.36, 5.891, 4.92]}, {"time": 6, "value": 5.68}]}, "body": {"rotate": [{"value": 5.76}]}, "Arm_R": {"rotate": [{"value": 33.08, "curve": [0.156, 31.91, 0.312, 31.05]}, {"time": 0.4667, "value": 31.05, "curve": [0.557, 31.05, 0.647, 31.35]}, {"time": 0.7333, "value": 31.81, "curve": [1.147, 33.94, 1.557, 39.83]}, {"time": 1.9667, "value": 39.83, "curve": [2.467, 39.83, 2.967, 31.05]}, {"time": 3.4667, "value": 31.05, "curve": [3.967, 31.05, 4.467, 39.83]}, {"time": 4.9667, "value": 39.83, "curve": [5.245, 39.83, 5.523, 37.13]}, {"time": 5.8, "value": 34.72, "curve": [5.867, 34.14, 5.934, 33.59]}, {"time": 6, "value": 33.08}], "translate": [{}]}, "Arm_R2": {"rotate": [{"value": -3.26, "curve": [0.246, -5.21, 0.489, -7.13]}, {"time": 0.7333, "value": -7.13, "curve": [1.233, -7.13, 1.733, 0.88]}, {"time": 2.2333, "value": 0.88, "curve": [2.733, 0.88, 3.233, -7.13]}, {"time": 3.7333, "value": -7.13, "curve": [4.233, -7.13, 4.733, 0.88]}, {"time": 5.2333, "value": 0.88, "curve": [5.423, 0.88, 5.613, -0.26]}, {"time": 5.8, "value": -1.7, "curve": [5.867, -2.2, 5.934, -2.73]}, {"time": 6, "value": -3.26}]}, "Arm_L": {"rotate": [{"value": 11.09, "curve": [0.313, 9.3, 0.623, 6.94]}, {"time": 0.9333, "value": 6.94, "curve": [1.433, 6.94, 1.933, 13.06]}, {"time": 2.4333, "value": 13.06, "curve": [2.933, 13.06, 3.433, 6.94]}, {"time": 3.9333, "value": 6.94, "curve": [4.433, 6.94, 4.933, 13.06]}, {"time": 5.4333, "value": 13.06, "curve": [5.623, 13.06, 5.813, 12.19]}, {"time": 6, "value": 11.09}]}, "Arm_L2": {"rotate": [{"value": 30.38, "curve": [0.223, 25.16, 0.445, 20.42]}, {"time": 0.6667, "value": 20.42, "curve": [0.757, 20.42, 0.847, 21.23]}, {"time": 0.9333, "value": 22.5, "curve": [1.347, 28.26, 1.757, 44.25]}, {"time": 2.1667, "value": 44.25, "curve": [2.667, 44.25, 3.167, 20.42]}, {"time": 3.6667, "value": 20.42, "curve": [4.167, 20.42, 4.667, 44.25]}, {"time": 5.1667, "value": 44.25, "curve": [5.445, 44.25, 5.723, 36.92]}, {"time": 6, "value": 30.38}]}, "Mouth_control": {"rotate": [{"value": -44.48, "curve": [0.122, -43.29, 0.244, -42.35]}, {"time": 0.3667, "value": -42.35, "curve": [0.7, -42.35, 1.033, -49.3]}, {"time": 1.3667, "value": -49.3, "curve": [1.7, -49.3, 2.033, -42.35]}, {"time": 2.3667, "value": -42.35, "curve": [2.7, -42.35, 3.033, -49.3]}, {"time": 3.3667, "value": -49.3, "curve": [3.7, -49.3, 4.033, -42.35]}, {"time": 4.3667, "value": -42.35, "curve": [4.7, -42.35, 5.033, -49.3]}, {"time": 5.3667, "value": -49.3, "curve": [5.578, -49.3, 5.789, -46.52]}, {"time": 6, "value": -44.48}]}, "blot": {"translate": [{}], "scale": [{}]}, "cntr": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_Reaction": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}]}, "blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"pupil": {"translate": [{"x": 1.86, "y": 0.17, "curve": [0.022, 1.86, 0.044, 5.72, 0.022, 0.17, 0.044, -19.17]}, {"time": 0.0667, "x": 5.72, "y": -19.17, "curve": "stepped"}, {"time": 0.2667, "x": 5.72, "y": -19.17, "curve": [0.389, 5.72, 0.511, 4.69, 0.389, -19.17, 0.511, -23.09]}, {"time": 0.6333, "x": 4.69, "y": -23.09}], "scale": [{"curve": [0.017, 1.102, 0.05, 0.559, 0.017, 1.102, 0.05, 0.559]}, {"time": 0.0667, "x": 0.559, "y": 0.559, "curve": "stepped"}, {"time": 0.2, "x": 0.559, "y": 0.559, "curve": [0.222, 0.559, 0.244, 0.864, 0.222, 0.559, 0.244, 0.864]}, {"time": 0.2667}]}, "Eyelid": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"curve": [0.022, 0, 0.046, 21.34, 0.022, 0, 0.044, 1.23]}, {"time": 0.0667, "x": 21.34, "y": 1.23, "curve": [0.089, 21.34, 0.111, 11.62, 0.089, 1.23, 0.111, 0.29]}, {"time": 0.1333, "x": 11.62, "y": 0.29, "curve": [0.156, 11.62, 0.178, 23.76, 0.156, 0.29, 0.178, -2.53]}, {"time": 0.2, "x": 23.76, "y": -2.53, "curve": [0.222, 23.76, 0.244, 22.15, 0.222, -2.53, 0.244, -0.02]}, {"time": 0.2667, "x": 21.34, "y": 1.23, "curve": [0.388, 16.94, 0.511, -14.63, 0.387, 8.02, 0.511, -1.18]}, {"time": 0.6333, "x": -14.63, "y": -1.18}]}, "mouth_base": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"x": 1.63, "y": 0.44, "curve": [0.022, 1.63, 0.044, 5.98, 0.022, 0.44, 0.044, 0.44]}, {"time": 0.0667, "x": 5.98, "y": 0.44, "curve": "stepped"}, {"time": 0.2667, "x": 5.98, "y": 0.44, "curve": [0.389, 5.98, 0.511, -3.11, 0.389, 0.44, 0.511, 0]}, {"time": 0.6333, "x": -3.11}], "scale": [{"y": 0.973, "curve": "stepped"}, {"time": 0.2667, "y": 0.973, "curve": [0.389, 1, 0.511, 1, 0.389, 0.973, 0.511, 1.021]}, {"time": 0.6333, "y": 1.021}]}, "eye": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"curve": "stepped"}, {"time": 0.6333}], "scale": [{}]}, "Face": {"translate": [{"y": -5.55, "curve": [0.022, 0, 0.044, 6.62, 0.022, -5.55, 0.044, -5.55]}, {"time": 0.0667, "x": 6.62, "y": -5.55, "curve": "stepped"}, {"time": 0.2667, "x": 6.62, "y": -5.55, "curve": [0.389, 6.62, 0.511, 0, 0.389, -5.55, 0.511, -5.55]}, {"time": 0.6333, "y": -5.55}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 0.6333, "x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 26.48]}, {"time": 0.0667, "y": 26.48, "curve": [0.085, 0, 0.108, 0, 0.085, 26.48, 0.108, 23.18]}, {"time": 0.1333, "y": 23.18, "curve": [0.155, 0, 0.178, 0, 0.156, 23.18, 0.178, 28.71]}, {"time": 0.2, "y": 29.73, "curve": [0.213, 0, 0.255, 0, 0.213, 30.34, 0.255, 26.48]}, {"time": 0.2667, "y": 26.48, "curve": [0.389, 0, 0.511, 0, 0.389, 26.48, 0.511, 0]}, {"time": 0.6333}], "scale": [{"x": 1.009, "y": 0.991, "curve": [0.022, 1.009, 0.044, 0.9, 0.022, 0.991, 0.044, 1.1]}, {"time": 0.0667, "x": 0.9, "y": 1.1, "curve": [0.085, 0.9, 0.108, 0.969, 0.085, 1.1, 0.108, 1.031]}, {"time": 0.1333, "x": 0.95, "y": 1.05, "curve": [0.156, 0.933, 0.178, 0.9, 0.156, 1.067, 0.178, 1.1]}, {"time": 0.2, "x": 0.9, "y": 1.1, "curve": "stepped"}, {"time": 0.2667, "x": 0.9, "y": 1.1, "curve": [0.389, 0.9, 0.511, 1.009, 0.389, 1.1, 0.511, 0.991]}, {"time": 0.6333, "x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 1.32, "curve": [0.022, 1.32, 0.044, -16.51]}, {"time": 0.0667, "value": -16.51, "curve": "stepped"}, {"time": 0.2667, "value": -16.51, "curve": [0.389, -16.51, 0.511, 5.68]}, {"time": 0.6333, "value": 5.68}]}, "body": {"rotate": [{"curve": [0.022, 0, 0.044, 5.97]}, {"time": 0.0667, "value": 5.97, "curve": "stepped"}, {"time": 0.2667, "value": 5.97, "curve": [0.389, 5.97, 0.511, 5.76]}, {"time": 0.6333, "value": 5.76}]}, "Arm_R": {"rotate": [{"value": -1.38, "curve": [0.022, -1.38, 0.044, -17.96]}, {"time": 0.0667, "value": -17.96, "curve": "stepped"}, {"time": 0.2667, "value": -17.96, "curve": [0.389, -17.96, 0.511, 33.08]}, {"time": 0.6333, "value": 33.08}], "translate": [{"curve": [0.022, 0, 0.044, 5.01, 0.022, 0, 0.044, -0.53]}, {"time": 0.0667, "x": 5.01, "y": -0.53, "curve": "stepped"}, {"time": 0.2667, "x": 5.01, "y": -0.53, "curve": [0.389, 5.01, 0.511, 0, 0.389, -0.53, 0.511, 0]}, {"time": 0.6333}]}, "Arm_R2": {"rotate": [{"value": -7.3, "curve": [0.022, -7.3, 0.044, -23.35]}, {"time": 0.0667, "value": -23.35, "curve": "stepped"}, {"time": 0.2667, "value": -23.35, "curve": [0.389, -23.35, 0.511, -3.26]}, {"time": 0.6333, "value": -3.26}]}, "Arm_L": {"rotate": [{"value": 4.12, "curve": [0.022, 4.12, 0.044, 12.29]}, {"time": 0.0667, "value": 12.29, "curve": "stepped"}, {"time": 0.2667, "value": 12.29, "curve": [0.389, 12.29, 0.511, 11.09]}, {"time": 0.6333, "value": 11.09}]}, "Arm_L2": {"rotate": [{"value": 15.74, "curve": [0.022, 15.74, 0.044, 27.61]}, {"time": 0.0667, "value": 27.61, "curve": "stepped"}, {"time": 0.2667, "value": 27.61, "curve": [0.389, 27.61, 0.511, 30.38]}, {"time": 0.6333, "value": 30.38}]}, "Mouth_control": {"rotate": [{"curve": [0.022, 0, 0.044, 23.68]}, {"time": 0.0667, "value": 23.68, "curve": "stepped"}, {"time": 0.2667, "value": 23.68, "curve": [0.389, 23.68, 0.511, -44.48]}, {"time": 0.6333, "value": -44.48}]}, "blot": {"translate": [{}], "scale": [{}]}, "cntr": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t1_Reaction2": {"slots": {"Arm_L": {"attachment": [{"name": "Arm_L"}]}, "ARM_L_outline": {"attachment": [{"name": "ARM_L_outline"}]}, "Arm_R": {"attachment": [{"name": "Arm_R"}]}, "ARM_R_outline": {"attachment": [{"name": "ARM_R_outline"}]}, "blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "body": {"attachment": [{"name": "body"}]}, "body_outline": {"attachment": [{"name": "body_outline"}]}, "eye": {"attachment": [{"name": "eye"}]}, "eyebrow": {"attachment": [{"name": "eyebrow"}]}, "eyebrow_base": {"attachment": [{"name": "eyebrow_base"}]}, "mouth": {"attachment": [{"name": "mouth"}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}]}, "mouth_outline": {"attachment": [{"name": "mouth_outline"}]}, "pupil": {"attachment": [{"name": "pupil"}]}}, "bones": {"pupil": {"translate": [{"x": 1.86, "y": 0.17, "curve": [0.022, 1.86, 0.044, 5.72, 0.022, 0.17, 0.044, -19.17]}, {"time": 0.0667, "x": 5.72, "y": -19.17, "curve": "stepped"}, {"time": 0.2333, "x": 5.72, "y": -19.17, "curve": [0.367, 5.72, 0.5, 4.69, 0.367, -19.17, 0.5, -23.09]}, {"time": 0.6333, "x": 4.69, "y": -23.09}], "scale": [{"curve": [0.017, 1.102, 0.05, 0.559, 0.017, 1.102, 0.05, 0.559]}, {"time": 0.0667, "x": 0.559, "y": 0.559, "curve": [0.085, 0.559, 0.215, 0.886, 0.085, 0.559, 0.215, 0.886]}, {"time": 0.2333}]}, "Eyelid": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"curve": [0.022, 0, 0.046, 21.34, 0.022, 0, 0.044, 1.23]}, {"time": 0.0667, "x": 21.34, "y": 1.23, "curve": [0.086, 21.34, 0.214, 22.05, 0.086, 1.23, 0.214, 0.13]}, {"time": 0.2333, "x": 21.34, "y": 1.23, "curve": [0.366, 16.54, 0.5, -14.63, 0.365, 8.64, 0.5, -1.18]}, {"time": 0.6333, "x": -14.63, "y": -1.18}]}, "mouth_base": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"x": 1.63, "y": 0.44, "curve": [0.022, 1.63, 0.044, 5.98, 0.022, 0.44, 0.044, 0.44]}, {"time": 0.0667, "x": 5.98, "y": 0.44, "curve": "stepped"}, {"time": 0.2333, "x": 5.98, "y": 0.44, "curve": [0.367, 5.98, 0.5, -3.11, 0.367, 0.44, 0.5, 0]}, {"time": 0.6333, "x": -3.11}], "scale": [{"y": 0.973, "curve": "stepped"}, {"time": 0.2333, "y": 0.973, "curve": [0.367, 1, 0.5, 1, 0.367, 0.973, 0.5, 1.021]}, {"time": 0.6333, "y": 1.021}]}, "eye": {"rotate": [{"curve": "stepped"}, {"time": 0.6333}], "translate": [{"curve": "stepped"}, {"time": 0.6333}], "scale": [{}]}, "Face": {"translate": [{"y": -5.55, "curve": [0.022, 0, 0.044, 6.62, 0.022, -5.55, 0.044, -5.55]}, {"time": 0.0667, "x": 6.62, "y": -5.55, "curve": "stepped"}, {"time": 0.2333, "x": 6.62, "y": -5.55, "curve": [0.367, 6.62, 0.5, 0, 0.367, -5.55, 0.5, -5.55]}, {"time": 0.6333, "y": -5.55}]}, "eyebrow": {"translate": [{"x": 1.22, "y": -5.15, "curve": "stepped"}, {"time": 0.6333, "x": 1.22, "y": -5.15}]}, "low_cntr": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.045, 21.24]}, {"time": 0.0667, "y": 26.48, "curve": [0.083, 0, 0.223, 0, 0.125, 40.54, 0.172, 32.29]}, {"time": 0.2333, "y": 26.48, "curve": [0.367, 0, 0.5, 0, 0.366, 13.87, 0.5, 0]}, {"time": 0.6333}], "scale": [{"x": 1.009, "y": 0.991, "curve": [0.022, 1.009, 0.044, 0.913, 0.022, 0.991, 0.044, 1.083]}, {"time": 0.0667, "x": 0.9, "y": 1.1, "curve": [0.127, 0.865, 0.175, 0.881, 0.129, 1.148, 0.175, 1.12]}, {"time": 0.2333, "x": 0.9, "y": 1.1, "curve": [0.367, 0.944, 0.5, 1.009, 0.367, 1.055, 0.5, 0.991]}, {"time": 0.6333, "x": 1.009, "y": 0.991}]}, "body2": {"rotate": [{"value": 1.32, "curve": [0.022, 1.32, 0.044, -16.51]}, {"time": 0.0667, "value": -16.51, "curve": "stepped"}, {"time": 0.2333, "value": -16.51, "curve": [0.367, -16.51, 0.5, 5.68]}, {"time": 0.6333, "value": 5.68}]}, "body": {"rotate": [{"curve": [0.022, 0, 0.044, 5.97]}, {"time": 0.0667, "value": 5.97, "curve": "stepped"}, {"time": 0.2333, "value": 5.97, "curve": [0.367, 5.97, 0.5, 5.76]}, {"time": 0.6333, "value": 5.76}]}, "Arm_R": {"rotate": [{"value": -1.38, "curve": [0.022, -1.38, 0.044, -17.96]}, {"time": 0.0667, "value": -17.96, "curve": "stepped"}, {"time": 0.2333, "value": -17.96, "curve": [0.367, -17.96, 0.5, 33.08]}, {"time": 0.6333, "value": 33.08}], "translate": [{"curve": [0.022, 0, 0.044, 5.01, 0.022, 0, 0.044, -0.53]}, {"time": 0.0667, "x": 5.01, "y": -0.53, "curve": "stepped"}, {"time": 0.2333, "x": 5.01, "y": -0.53, "curve": [0.367, 5.01, 0.5, 0, 0.367, -0.53, 0.5, 0]}, {"time": 0.6333}]}, "Arm_R2": {"rotate": [{"value": -7.3, "curve": [0.022, -7.3, 0.044, -23.35]}, {"time": 0.0667, "value": -23.35, "curve": "stepped"}, {"time": 0.2333, "value": -23.35, "curve": [0.367, -23.35, 0.5, -3.26]}, {"time": 0.6333, "value": -3.26}]}, "Arm_L": {"rotate": [{"value": 4.12, "curve": [0.022, 4.12, 0.044, 12.29]}, {"time": 0.0667, "value": 12.29, "curve": "stepped"}, {"time": 0.2333, "value": 12.29, "curve": [0.367, 12.29, 0.5, 11.09]}, {"time": 0.6333, "value": 11.09}]}, "Arm_L2": {"rotate": [{"value": 15.74, "curve": [0.022, 15.74, 0.044, 27.61]}, {"time": 0.0667, "value": 27.61, "curve": "stepped"}, {"time": 0.2333, "value": 27.61, "curve": [0.367, 27.61, 0.5, 30.38]}, {"time": 0.6333, "value": 30.38}]}, "Mouth_control": {"rotate": [{"curve": [0.022, 0, 0.044, 19.58]}, {"time": 0.0667, "value": 19.58, "curve": "stepped"}, {"time": 0.2333, "value": 19.58, "curve": [0.367, 19.58, 0.5, -44.48]}, {"time": 0.6333, "value": -44.48}]}, "blot": {"translate": [{}], "scale": [{}]}, "cntr": {"translate": [{}], "scale": [{}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}}}, "t2_eye_down": {"bones": {"pupil": {"rotate": [{}], "translate": [{"x": 4.67, "y": -24.16}]}}}, "t2_eye_left": {"bones": {"pupil": {"rotate": [{"value": 10.88}], "translate": [{"x": -14.06, "y": -10.47}]}}}, "t2_eye_left-down": {"bones": {"pupil": {"rotate": [{"value": 10.88}], "translate": [{"x": -7.39, "y": -20.44}]}}}, "t2_eye_right": {"bones": {"pupil": {"rotate": [{"value": -3.08}], "translate": [{"x": 23.66, "y": -10.29}]}}}, "t2_eye_right-down": {"bones": {"pupil": {"rotate": [{"value": -3.08}], "translate": [{"x": 16.76, "y": -21.52}]}}}}}