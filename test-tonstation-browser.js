// Browser console test for TON Station API
// Copy and paste this into your browser console or use in your Vue app

const TELEGRAM_ID = '290777829'

// Import the service functions (if running in Vue app)
// import { checkTonstationDailyCombo, checkTonstationLuckyWheel } from '@/services/partners/tonstation'

// Direct API test functions (for browser console)
async function testTonstationAPI() {
  console.log('🚀 Testing TON Station API with Telegram ID:', TELEGRAM_ID)
  
  // Test Daily Combo
  console.log('\n🎯 Testing Daily Combo...')
  try {
    const dailyComboUrl = 'https://dev.tonstation.app/openapi/api/v1/dailyCombo/290777829/completion'
    const dailyComboResponse = await fetch(dailyComboUrl, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'X-API-Key': '4397cc1c-fbd0-4a7f-8b04-a225e758924d'
      }
    })
    
    console.log('Daily Combo Status:', dailyComboResponse.status)
    if (dailyComboResponse.ok) {
      const dailyComboData = await dailyComboResponse.json()
      console.log('Daily Combo Response:', dailyComboData)
      console.log('Daily Combo Completed:', dailyComboData.data?.completed || false)
    } else {
      console.log('Daily Combo Error:', await dailyComboResponse.text())
    }
  } catch (error) {
    console.error('Daily Combo Error:', error)
  }
  
  // Test Lucky Wheel
  console.log('\n🎰 Testing Lucky Wheel...')
  try {
    const dateStart = new Date()
    dateStart.setDate(dateStart.getDate() - 30)
    const dateStartISO = dateStart.toISOString()
    
    const luckyWheelUrl = `https://dev.tonstation.app/openapi/api/v1/luckyWheel/290777829/spinsCount?dateStart=${encodeURIComponent(dateStartISO)}`
    const luckyWheelResponse = await fetch(luckyWheelUrl, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'X-API-Key': '4397cc1c-fbd0-4a7f-8b04-a225e758924d'
      }
    })
    
    console.log('Lucky Wheel Status:', luckyWheelResponse.status)
    if (luckyWheelResponse.ok) {
      const luckyWheelData = await luckyWheelResponse.json()
      console.log('Lucky Wheel Response:', luckyWheelData)
      const spinCount = luckyWheelData.data?.count || 0
      console.log('Lucky Wheel Spins:', spinCount)
      console.log('Lucky Wheel Requirement Met (≥5):', spinCount >= 5)
    } else {
      console.log('Lucky Wheel Error:', await luckyWheelResponse.text())
    }
  } catch (error) {
    console.error('Lucky Wheel Error:', error)
  }
}

// Test using the actual service functions (if available)
async function testTonstationService() {
  console.log('🚀 Testing TON Station Service Functions with Telegram ID:', TELEGRAM_ID)
  
  try {
    // Uncomment these lines if running in your Vue app with the service imported
    // const dailyComboResult = await checkTonstationDailyCombo(TELEGRAM_ID)
    // const luckyWheelResult = await checkTonstationLuckyWheel(TELEGRAM_ID)
    // 
    // console.log('✅ Daily Combo Result:', dailyComboResult)
    // console.log('✅ Lucky Wheel Result:', luckyWheelResult)
    
    console.log('ℹ️ To test service functions, uncomment the lines above and run in your Vue app')
  } catch (error) {
    console.error('Service test error:', error)
  }
}

// Run the tests
console.log('📋 Available test functions:')
console.log('- testTonstationAPI() - Test direct API calls')
console.log('- testTonstationService() - Test service functions')
console.log('\nRun: testTonstationAPI()')

// Auto-run the API test
testTonstationAPI()
