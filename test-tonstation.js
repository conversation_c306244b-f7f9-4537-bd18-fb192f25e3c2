#!/usr/bin/env node

// Test script for TON Station API endpoints
// Usage: node test-tonstation.js

const TELEGRAM_ID = '290777829'

// API Configuration
const TONSTATION_BASE_URL_DEV = 'https://dev.tonstation.app/openapi/api/v1'
const TONSTATION_BASE_URL_PROD = 'https://tonstation.app/openapi/api/v1'
const TONSTATION_API_KEY_DEV = '4397cc1c-fbd0-4a7f-8b04-a225e758924d'
const TONSTATION_API_KEY_PROD = '2e88e828-265e-4b73-9d75-7738b243a0bc'

// Test both environments
const environments = [
  { name: 'DEV', baseUrl: TONSTATION_BASE_URL_DEV, apiKey: TONSTATION_API_KEY_DEV },
  { name: 'PROD', baseUrl: TONSTATION_BASE_URL_PROD, apiKey: TONSTATION_API_KEY_PROD }
]

async function testDailyCombo(baseUrl, apiKey, envName) {
  const url = `${baseUrl}/dailyCombo/${TELEGRAM_ID}/completion`
  
  console.log(`\n🎯 Testing Daily Combo (${envName})`)
  console.log(`URL: ${url}`)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'X-API-Key': apiKey
      }
    })
    
    console.log(`Status: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('Response:', JSON.stringify(data, null, 2))
      
      if (data.code === 200 && data.message === 'success') {
        console.log(`✅ Daily Combo Status: ${data.data.completed ? 'COMPLETED' : 'NOT COMPLETED'}`)
        return data.data.completed
      } else {
        console.log('❌ API returned non-success response')
        return false
      }
    } else {
      const errorText = await response.text()
      console.log(`❌ HTTP Error: ${errorText}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`)
    return false
  }
}

async function testLuckyWheel(baseUrl, apiKey, envName) {
  // Get date from 30 days ago
  const dateStart = new Date()
  dateStart.setDate(dateStart.getDate() - 30)
  const dateStartISO = dateStart.toISOString()
  
  const url = `${baseUrl}/luckyWheel/${TELEGRAM_ID}/spinsCount?dateStart=${encodeURIComponent(dateStartISO)}`
  
  console.log(`\n🎰 Testing Lucky Wheel (${envName})`)
  console.log(`URL: ${url}`)
  console.log(`Date Start: ${dateStartISO}`)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'X-API-Key': apiKey
      }
    })
    
    console.log(`Status: ${response.status} ${response.statusText}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('Response:', JSON.stringify(data, null, 2))
      
      if (data.code === 200 && data.message === 'success') {
        const spinCount = data.data.count || 0
        console.log(`✅ Lucky Wheel Spins: ${spinCount}`)
        console.log(`✅ Requirement Met (≥5 spins): ${spinCount >= 5 ? 'YES' : 'NO'}`)
        return spinCount >= 5
      } else {
        console.log('❌ API returned non-success response')
        return false
      }
    } else {
      const errorText = await response.text()
      console.log(`❌ HTTP Error: ${errorText}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`)
    return false
  }
}

async function runTests() {
  console.log('🚀 Testing TON Station API Endpoints')
  console.log(`📱 Telegram ID: ${TELEGRAM_ID}`)
  console.log('=' * 50)
  
  for (const env of environments) {
    console.log(`\n🌍 Testing ${env.name} Environment`)
    console.log('-'.repeat(30))
    
    const dailyComboResult = await testDailyCombo(env.baseUrl, env.apiKey, env.name)
    const luckyWheelResult = await testLuckyWheel(env.baseUrl, env.apiKey, env.name)
    
    console.log(`\n📊 ${env.name} Summary:`)
    console.log(`   Daily Combo: ${dailyComboResult ? '✅ COMPLETED' : '❌ NOT COMPLETED'}`)
    console.log(`   Lucky Wheel: ${luckyWheelResult ? '✅ REQUIREMENT MET' : '❌ REQUIREMENT NOT MET'}`)
  }
  
  console.log('\n🏁 Test completed!')
}

// Run the tests
runTests().catch(console.error)
